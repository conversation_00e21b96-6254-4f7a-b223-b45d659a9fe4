{"Version": 1, "WorkspaceRootPath": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\mini_booster_session.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\mini_booster_session.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\console_controller.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\console_controller.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\include\\scenarios\\high_concurrency_runner.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:include\\scenarios\\high_concurrency_runner.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\connection_stress_test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\connection_stress_test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include\\vector||{3B902123-F8A7-4915-9F01-361F908088D0}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.22621.0\\ucrt\\misc\\invalid_parameter.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|CMake 概述页||{B1CAA5B0-FEB1-4350-8AB9-F895876842F2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "high_concurrency_runner.h", "DocumentMoniker": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\include\\scenarios\\high_concurrency_runner.h", "RelativeDocumentMoniker": "include\\scenarios\\high_concurrency_runner.h", "ToolTip": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\include\\scenarios\\high_concurrency_runner.h", "RelativeToolTip": "include\\scenarios\\high_concurrency_runner.h", "ViewState": "AgIAACsAAAAAAAAAAAAnwGoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-31T02:12:56.437Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "mini_booster_session.cpp", "DocumentMoniker": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\mini_booster_session.cpp", "RelativeDocumentMoniker": "src\\mini_booster_session.cpp", "ToolTip": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\mini_booster_session.cpp", "RelativeToolTip": "src\\mini_booster_session.cpp", "ViewState": "AgIAAKwBAAAAAAAAAAAIwC8CAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-03T03:50:51.955Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "connection_stress_test.cpp", "DocumentMoniker": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\connection_stress_test.cpp", "RelativeDocumentMoniker": "src\\connection_stress_test.cpp", "ToolTip": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\connection_stress_test.cpp", "RelativeToolTip": "src\\connection_stress_test.cpp", "ViewState": "AgIAAAYAAAAAAAAAAAAAwA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-03T03:50:38.831Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "invalid_parameter.cpp", "DocumentMoniker": "C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.22621.0\\ucrt\\misc\\invalid_parameter.cpp", "ToolTip": "C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.22621.0\\ucrt\\misc\\invalid_parameter.cpp", "ViewState": "AgIAAOEAAAAAAAAAAAAAwOwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-02T12:40:36.126Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "vector", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include\\vector", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include\\vector", "ViewState": "AgIAACIAAAAAAAAAAAAowDUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-02T12:05:31.085Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "console_controller.cpp", "DocumentMoniker": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\console_controller.cpp", "RelativeDocumentMoniker": "src\\console_controller.cpp", "ToolTip": "E:\\study\\cursor\\Test-tool\\gateway-load-tester\\mini_booster\\src\\console_controller.cpp", "RelativeToolTip": "src\\console_controller.cpp", "ViewState": "AgIAAPcFAAAAAAAAAAAAAPcFAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-30T07:46:41.501Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "CMake 概述页", "DocumentMoniker": "CMake 概述页", "ToolTip": "CMake 概述页", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-30T05:01:34.468Z"}]}]}]}