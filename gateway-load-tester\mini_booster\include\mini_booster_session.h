#pragma once

#include <string>
#include <cstdint>
#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <deque>
#include <chrono>
#include <boost/asio.hpp>
#include <queue>

#include "../../include/protocol/rule_protocol.h"
// #include "tunnel_channel.h"  // 隧道功能已关闭

#ifndef MINI_BOOSTER_STATS_FWD
#define MINI_BOOSTER_STATS_FWD
struct MiniBoosterStats; // 前向声明，供 AutoStressResult 使用
#endif

namespace mini_booster {

/**
 * @brief 登录响应结果结构体
 */
struct LoginResult {
    std::string app_id;
    uint32_t user_id = 0;
    std::string listen_ip;
    uint16_t listen_port = 0;
};

/**
 * @brief P4-A 重连触发原因枚举
 */
enum class ReconnectTrigger {
    NETWORK_ERROR,          // 网络错误（connection_reset, broken_pipe等）
    HEARTBEAT_TIMEOUT,      // 心跳超时（连续3次心跳无响应）
    RANDOM_SIMULATION,      // 随机模拟断线（按配置概率）
    STRESS_INDUCED          // 压力测试中的自然断线
};

/**
 * @brief P4-A 会话状态枚举
 */
enum class SessionState {
    CONNECTING,         // 初始连接中
    CONNECTED,          // 已连接
    RECONNECTING,       // 重连中
    FAILED             // 重连失败
};

/**
 * @brief P4-A 重连配置结构体
 */
struct ReconnectConfig {
    bool enabled = true;                        // 是否启用重连
    uint32_t max_attempts = 5;                  // 最大重连次数
    uint32_t base_delay_ms = 5000;              // 重连固定延迟 5 秒
    uint32_t max_delay_ms = 5000;               // 与 base_delay_ms 保持一致，不做指数退避
    double failure_probability = 0.05;          // 5%概率模拟断线
    uint32_t heartbeat_timeout_threshold = 12;   // 心跳超时阈值（与龙盾客户端保持一致，60秒无响应才断线，5s一次心跳，约12次）
};

/**
 * @brief 单个应用的配置信息
 */
struct AppConfig {
    std::string app_id;                        // 应用ID
    uint32_t start_user_id = 100000;          // 该应用的起始用户ID
    uint32_t users_count = 100;               // 该应用的用户数量
    uint16_t gateway_port = 5188;             // 该应用专用的网关端口
    std::string description;                   // 应用描述
};

/**
 * @brief P5-A: 自动极限压测配置结构体
 */
struct AutoStressConfig {
    size_t batch_size = 100;                      // 每批次增加的连接数
    uint32_t interval_seconds = 2;                // 批次间隔时间（秒）
    double failure_threshold_percent = 5.0;       // 失败率阈值（百分比）
    size_t max_users = 5000;                      // 最大用户数上限
    uint32_t stats_period_seconds = 30;           // 统计周期（秒）
    size_t min_batch_connections = 10;            // 最小批次连接数（用于慢启动）
    bool enable_gradual_ramp = true;              // 是否启用渐进式增压
    uint32_t hold_duration_seconds = 30;          // 达到目标并发后继续保持的秒数 (0 表示不保持)
    
    // 报告配置
    std::string report_prefix = "autostress";     // 报告文件前缀
    bool auto_generate_reports = true;            // 是否自动生成报告
    bool verbose_output = true;                   // 是否显示详细输出
};

/**
 * @brief Mini-Booster 配置结构体
 */
struct MiniBoosterConfig {
    std::string gateway_host = "***************"; // 🔧 修复：使用正确的网关地址
    uint16_t gateway_port = 5188;              // 网关服务器端口
    std::string node_host = "***************"; // Nginx节点地址
    uint16_t node_port = 5188;                 // Nginx节点端口
    std::string app_id = "8888";               // 应用ID（单应用模式）
    uint32_t start_user_id = 1;                // 起始用户ID（单应用模式）
    uint32_t role_id = 0;                      // 角色ID：0=机器A，1=机器B
    uint32_t users_count = 0;                  // 🔧 修复：默认不启动用户，避免自动执行
    double   packets_per_second = 5.0;        // 每秒包数，从20pps降为5pps减少网络压力
    uint32_t packet_size = 1024;               // 数据包大小
    // —— 随机隧道负载新增字段 ——
    bool tunnel_random_payload = false;        // 是否随机化隧道负载大小
    uint32_t tunnel_packet_size_min = 256;     // 随机尺寸下限
    uint32_t tunnel_packet_size_max = 1024;    // 随机尺寸上限 (≤4KB 建议)
    uint32_t test_duration_seconds = 60;       // 测试持续时间(秒)
    
    // P3-C-1: 多应用配置支持
    std::vector<AppConfig> applications;       // 多应用配置列表
    bool multi_app_mode = false;               // 是否启用多应用模式
    
    // 心跳和维护配置
    bool enable_heartbeat = true;              // 是否启用心跳包
    uint32_t heartbeat_interval_seconds = 5;   // 心跳间隔（与龙盾客户端保持一致）
    uint32_t business_data_interval_ms = 500;  // 业务数据发送间隔（毫秒）- 从100ms改为500ms减少网络压力
    
    // —— Rule 长连接行为复刻（3.x） ——
    uint32_t rule_keepalive_sec = 10;         // 控制链最少保持秒数
    uint32_t rule_ping_interval_ms = 5000;    // Rule ping 周期改为5s（与龙盾客户端保持一致）
    bool     rule_single_conn = true;         // 每用户仅保持一条控制链

    // —— Data 通道配置 ——
    uint32_t data_channels_per_user = 1;      // 每用户业务链数量
    uint32_t data_idle_timeout_ms = 0;        // 空闲超时自动关闭(0=不关)
    
    // —— 批内连接抖动参数（2.1） ——
    uint32_t conn_jitter_min_ms = 20;   // 默认 20ms
    uint32_t conn_jitter_max_ms = 80;   // 默认 80ms
    
    // D-3 控制连接可选关闭配置
    bool close_control_after_handshake = false; // 握手完成后是否关闭控制连接("UI=1"效果)
    
    // D-2 第四隧道配置（已关闭）
    bool enable_data_tunnel = false;            // 隧道功能已彻底关闭
    uint32_t tunnel_heartbeat_interval_ms = 5000; // 隧道心跳间隔（毫秒）- 保留配置但不使用
    
    // —— 握手阶段超时（毫秒），默认 60 000（龙盾客户端容忍服务器排队长达60秒） ——
    uint32_t handshake_timeout_ms = 60000; // 与原版龙盾保持一致，60秒内未回包才视为失败
    // 新增：握手失败后自动重试次数（0=不重试）
    uint32_t handshake_retry = 0;
    
    // P3-C-1: 多应用测试配置
    bool enable_app_isolation_test = false;    // 是否启用应用隔离测试
    uint32_t app_statistics_check_interval_seconds = 10; // 应用统计检查间隔
    
    // P4-A 重连配置（新增）
    ReconnectConfig reconnect_config;          // 重连配置
    
    // P5-A 自动极限压测配置（新增）
    AutoStressConfig auto_stress_config;       // 自动极限压测配置
};

/**
 * @brief P5-C: 自动极限压测批次统计
 */
struct AutoStressBatchStats {
    size_t batch_number = 0;                   // 批次编号
    size_t total_users = 0;                    // 当前总用户数
    size_t successful_connections = 0;         // 成功连接数
    size_t failed_connections = 0;             // 失败连接数
    size_t total_attempts = 0;                 // 尝试握手总数
    size_t online_users = 0;                   // 当前在线连接（活跃控制连接）
    double failure_rate_percent = 0.0;         // 失败率百分比
    std::chrono::steady_clock::time_point timestamp; // 时间戳
    double connections_per_second = 0.0;       // 当前CPS
};

/**
 * @brief Mini-Booster 统计信息 (提前放到 AutoStressResult 之前，确保完全定义)
 */
struct MiniBoosterStats {
    uint64_t total_handshakes_sent = 0;        // 总发送握手数
    uint64_t successful_handshakes = 0;        // 成功握手数
    uint64_t failed_handshakes = 0;            // 失败握手数
    uint64_t timeout_handshakes = 0;           // 超时握手数 (新增)
    uint64_t active_control_connections = 0;   // 当前活跃控制连接数
    uint64_t active_business_connections = 0;  // 当前活跃业务连接数
    uint64_t business_sessions_established = 0; // 成功建立的业务Session数
    uint64_t total_bytes_sent = 0;             // 总发送字节数
    uint64_t total_bytes_received = 0;         // 总接收字节数
    uint64_t heartbeat_sent = 0;               // 发送的心跳包数量
    uint64_t heartbeat_responses = 0;          // 收到的心跳响应数量
    uint64_t business_packets_sent = 0;        // 发送的业务数据包数量
    uint64_t connection_drops = 0;             // 连接意外断开次数
    
    // D-4 隧道流量统计（新增）
    uint64_t active_data_tunnels = 0;          // 当前活跃数据隧道数
    uint64_t data_tunnels_established = 0;     // 成功建立的数据隧道数
    uint64_t business_packets_sent_tunnel = 0; // 通过隧道发送的业务数据包数量
    uint64_t bytes_sent_tunnel = 0;            // 通过隧道发送的字节数
    uint64_t tunnel_heartbeat_sent = 0;        // 发送的隧道心跳包数量
    
    // P4-A 重连统计（新增）
    uint64_t reconnect_attempts = 0;           // 重连尝试次数
    uint64_t reconnect_successes = 0;          // 重连成功次数
    uint64_t reconnect_failures = 0;           // 重连失败次数
    uint64_t heartbeat_timeouts = 0;           // 心跳超时次数
    uint64_t network_errors = 0;               // 网络错误次数
    
    // 重连时间统计 (毫秒)
    uint64_t total_reconnect_time_ms = 0;      // 总重连时间
    uint64_t min_reconnect_time_ms = UINT64_MAX; // 最小重连时间
    uint64_t max_reconnect_time_ms = 0;        // 最大重连时间
    
    // 延迟统计 (微秒)
    uint64_t min_latency_us = UINT64_MAX;      // 最小延迟
    uint64_t max_latency_us = 0;               // 最大延迟
    uint64_t total_latency_us = 0;             // 总延迟(用于计算平均值)
    
    void reset() {
        total_handshakes_sent = 0;
        successful_handshakes = 0;
        failed_handshakes = 0;
        timeout_handshakes = 0;
        active_control_connections = 0;
        active_business_connections = 0;
        business_sessions_established = 0;
        total_bytes_sent = 0;
        total_bytes_received = 0;
        heartbeat_sent = 0;
        heartbeat_responses = 0;
        business_packets_sent = 0;
        connection_drops = 0;
        
        // D-4 隧道统计重置
        active_data_tunnels = 0;
        data_tunnels_established = 0;
        business_packets_sent_tunnel = 0;
        bytes_sent_tunnel = 0;
        tunnel_heartbeat_sent = 0;
        
        reconnect_attempts = 0;
        reconnect_successes = 0;
        reconnect_failures = 0;
        heartbeat_timeouts = 0;
        network_errors = 0;
        
        total_reconnect_time_ms = 0;
        min_reconnect_time_ms = UINT64_MAX;
        max_reconnect_time_ms = 0;
        
        min_latency_us = UINT64_MAX;
        max_latency_us = 0;
        total_latency_us = 0;
    }
};

/**
 * @brief P5-C: 自动极限压测汇总结果
 */
struct AutoStressResult {
    size_t max_concurrent_users = 0;           // 达到的最大并发用户数
    double peak_cps = 0.0;                     // 峰值CPS
    size_t total_batches = 0;                  // 总批次数
    std::vector<AutoStressBatchStats> batch_history; // 批次历史记录
    std::chrono::steady_clock::time_point start_time; // 开始时间
    std::chrono::steady_clock::time_point end_time;   // 结束时间
    std::string stop_reason;                   // 停止原因
    bool success = false;                      // 是否成功完成

    std::unique_ptr<MiniBoosterStats> final_stats; // 停止时的完整会话统计（成功/失败/隧道/流量等）

    // —— 新增: 方便后续报告的关键指标 ——
    size_t failure_batch_number = 0;           // 触发失败阈值的批次号
    double failure_rate_at_stop = 0.0;         // 触发时的失败率%
    size_t io_thread_count = 0;                // 客户端IO线程数

    // -- 拷贝语义支持（因 unique_ptr 需要手写） --
    AutoStressResult() = default;

    // 自定义拷贝构造函数，执行深拷贝
    AutoStressResult(const AutoStressResult& other)
        : max_concurrent_users(other.max_concurrent_users),
          peak_cps(other.peak_cps),
          total_batches(other.total_batches),
          batch_history(other.batch_history),
          start_time(other.start_time),
          end_time(other.end_time),
          stop_reason(other.stop_reason),
          success(other.success),
          failure_batch_number(other.failure_batch_number),
          failure_rate_at_stop(other.failure_rate_at_stop),
          io_thread_count(other.io_thread_count) {
        if (other.final_stats) {
            final_stats = std::make_unique<MiniBoosterStats>(*other.final_stats);
        }
    }

    // 自定义拷贝赋值运算符，执行深拷贝
    AutoStressResult& operator=(const AutoStressResult& other) {
        if (this == &other) return *this;
        max_concurrent_users = other.max_concurrent_users;
        peak_cps = other.peak_cps;
        total_batches = other.total_batches;
        batch_history = other.batch_history;
        start_time = other.start_time;
        end_time = other.end_time;
        stop_reason = other.stop_reason;
        success = other.success;
        failure_batch_number = other.failure_batch_number;
        failure_rate_at_stop = other.failure_rate_at_stop;
        io_thread_count = other.io_thread_count;
        if (other.final_stats) {
            final_stats = std::make_unique<MiniBoosterStats>(*other.final_stats);
        } else {
            final_stats.reset();
        }
        return *this;
    }

    // 允许移动语义使用默认实现
    AutoStressResult(AutoStressResult&&) noexcept = default;
    AutoStressResult& operator=(AutoStressResult&&) noexcept = default;
};

/**
 * @brief 握手结果回调类型
 */
using HandshakeCallback = std::function<void(
    uint32_t user_id,
    bool success,
    uint64_t latency_us,
    const std::string& json_response,
    const std::string& error_message
)>;

/**
 * @brief Mini-Booster 主类
 */
class MiniBoosterSession {
public:
    explicit MiniBoosterSession(boost::asio::io_context& io_context);
    ~MiniBoosterSession();
    
    // 基本控制接口
    void configure(const MiniBoosterConfig& config);
    void set_handshake_callback(HandshakeCallback callback);
    bool start();
    void stop();
    bool is_running() const;
    
    // P2-B-2: 动态扩缩容接口
    bool scale_up(int count);    // 动态增加连接数
    bool scale_down(int count);  // 动态减少连接数
    
    // 统计接口
    MiniBoosterStats get_stats() const;
    void reset_stats();
    void print_status() const;

    // 统计当前活跃会话数量（握手完成 + 隧道建立 + 心跳正常）
    size_t count_active_sessions() const;

    // 统计更新方法
    void update_stats_on_handshake_success();
    void update_stats_on_handshake_failure();
    void update_stats_on_heartbeat_sent();
    void update_stats_on_heartbeat_response();
    void update_stats_on_business_packet_sent();
    void update_stats_on_connection_drop();
    void update_stats_on_business_session_established();
    void update_stats_on_bytes_sent(size_t bytes);
    
    // D-4 隧道统计更新方法（已关闭）
    // void update_stats_on_data_tunnel_established();
    // void update_stats_on_tunnel_packet_sent();
    // void update_stats_on_tunnel_bytes_sent(size_t bytes);
    // void update_stats_on_tunnel_heartbeat_sent();
    // void update_stats_on_data_tunnel_change(int delta);
    
    // P4-A 重连统计更新方法（新增）
    void update_stats_on_reconnect_attempt();
    void update_stats_on_reconnect_success(uint64_t reconnect_time_ms);
    void update_stats_on_reconnect_failure();
    void update_stats_on_heartbeat_timeout();
    void update_stats_on_network_error();

    void update_stats_on_business_session_terminated();

    void update_stats_on_control_connection_change(int delta);

    void update_stats_on_handshake_timeout();

private:
    /**
     * @brief 单个用户会话连接类
     */
    class UserSession : public std::enable_shared_from_this<UserSession> {
    public:
        UserSession(boost::asio::io_context& io_context, 
                   MiniBoosterSession& mini_booster, 
                   uint32_t user_id);
        ~UserSession();
        
        void start_handshake();
        void close();
        
        // 延迟5秒后执行真正的 socket close，用于等待 CLS/FIN
        void do_final_close();
        
        // 新增：公共访问方法，供MiniBoosterSession类访问私有成员
        void set_should_stop(bool value) { session_should_stop_ = value; }
        // bool has_tunnel_channel() const { return tunnel_channel_ != nullptr; } // 隧道功能已关闭
        // void close_tunnel_channel(); // 隧道功能已关闭
        uint32_t get_user_id() const { return login_result_.user_id; }
        bool is_active(std::chrono::steady_clock::time_point now) const;
        
    private:
        void handle_connect(const boost::system::error_code& error);
        void send_handshake_packet();
        
        // 第一阶段：GameHeader
        void send_game_header();
        void handle_game_header_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_game_header_response(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 第二阶段：LOGIN消息
        void send_login_message();
        void handle_login_message_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_login_response(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 第三阶段：业务Session建立（连接到网关，发送56字节GameHeader）
        void establish_business_session();
        void handle_business_connect(const boost::system::error_code& error);
        void send_business_game_header();
        void handle_business_header_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        // 新增：业务链路 GameHeaderInject（12B）发送与回显处理
        void handle_business_inject_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_business_inject_echo(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_business_header_response(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 新增：业务通道登录 (PCOI Cmd1)
        void send_business_pcoi_login();
        void handle_business_pcoi_login_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 业务连接只读模式（照搬原版GameClient行为）
        void start_business_read_only();
        void handle_business_read(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 业务数据发送
        void start_business_data_sending();
        void send_business_data_packet();
        void handle_business_data_packet_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // D-2 第四隧道实现（已关闭）
        // void establish_data_tunnel();
        // void schedule_data_tunnel_start();
        // void handle_data_tunnel_connect(const boost::system::error_code& error);
        // void send_tunnel_header_inject(const std::function<void(const boost::system::error_code&)>& cb);
        // void send_tunnel_heartbeat();
        // void handle_tunnel_heartbeat_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        // void send_tunnel_business_data();
        // void handle_tunnel_business_data_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // 心跳维护
        void start_heartbeat();
        void send_heartbeat();
        void handle_heartbeat_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_heartbeat_response(const boost::system::error_code& error, std::size_t bytes_transferred);
        
        // === 新增：心跳监视 ===
        void start_heartbeat_monitor();
        
        // 控制连接消息处理
        void start_control_read();
        void handle_control_header_read(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_control_body_read(const boost::system::error_code& error, std::size_t bytes_transferred, uint32_t command_id, uint32_t sequence_id);
        void process_control_message(uint32_t command_id, const uint8_t* body_data, size_t body_size);
        void handle_control_error(const boost::system::error_code& error);
        
        // P4-A 重连处理方法（新增）
        bool analyze_error_for_reconnect(const boost::system::error_code& error);
        void start_reconnect_sequence(ReconnectTrigger trigger = ReconnectTrigger::NETWORK_ERROR);
        void execute_reconnect();
        void start_reconnect_handshake();
        void handle_reconnect_connect(const boost::system::error_code& error);
        void send_reconnect_game_header();
        void handle_reconnect_handshake_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_reconnect_handshake_response(const boost::system::error_code& error, std::size_t bytes_transferred);
        void handle_reconnect_success();
        void handle_reconnect_failure(const std::string& reason);
        void maybe_simulate_random_disconnect();
        void close_sockets_only();  // 只关闭socket，不更新统计
        
        // 错误处理
        void handle_connection_error(const std::string& context, const boost::system::error_code& error);
        void handle_timeout();
        void complete_handshake(bool success, const std::string& response_or_error);
        
        // 工具方法
        uint32_t get_session_id();
        uint32_t get_local_ip();
        std::string get_mac_address();
        bool parse_login_response(const std::string& json, LoginResult& result);
        uint32_t generate_session_id();
        // === 新增：握手超时处理 ===
        void handle_handshake_timeout();
        
        // 业务连接 KEEP-ALIVE
        void start_business_keepalive();
        void handle_business_keepalive_send(const boost::system::error_code& error, std::size_t bytes_transferred);
        void send_business_keepalive_now();
        
    private:
        boost::asio::ip::tcp::socket socket_;           // 控制连接
        boost::asio::ip::tcp::socket business_socket_;  // 业务连接
        // boost::asio::ip::tcp::socket data_tunnel_socket_; // D-2 第四隧道连接（已移除，改用网关代理）
        boost::asio::deadline_timer timeout_timer_;
        boost::asio::deadline_timer heartbeat_timer_;
        boost::asio::deadline_timer heartbeat_monitor_timer_; // 1s监视定时器
        boost::asio::deadline_timer business_data_timer_;        // 业务数据发送定时器
        // boost::asio::deadline_timer tunnel_heartbeat_timer_; // D-2 隧道心跳定时器（已移除）
        // boost::asio::deadline_timer tunnel_data_timer_;      // D-2 数据发送定时器（已移除，改用business_data_timer_）
        boost::asio::deadline_timer business_keepalive_timer_;         // 业务连接KEEP定时器
        boost::asio::deadline_timer reconnect_timer_;              // P4-A 重连定时器（新增）
        boost::asio::deadline_timer graceful_close_timer_;         // 优雅关闭定时器（新增）
        boost::asio::deadline_timer jitter_timer_;                 // 批内抖动定时器
        
        MiniBoosterSession& mini_booster_;
        uint32_t user_id_;
        bool completed_;
        bool business_session_established_;
        // bool data_tunnel_established_;               // D-2 数据隧道建立状态（已移除）
        bool control_connection_closed_;             // D-3 控制连接关闭状态（保留）
        
        // 🔧 步骤6：新增会话生命周期管理变量
        bool session_should_stop_;                   // 是否应该停止会话（替代completed_用于心跳检查）
        
        // P4-A 重连状态变量（新增）
        SessionState session_state_;                 // 会话状态
        uint32_t reconnect_attempts_;                // 当前重连尝试次数
        uint32_t saved_user_id_;                     // 保存的原始UserId（用于重连）
        uint32_t saved_session_id_;                  // 保存的原始SessionId（用于重连）
        uint32_t business_session_id_;               // 业务链路专用SessionId
        uint32_t consecutive_heartbeat_failures_;    // 连续心跳失败次数
        std::chrono::steady_clock::time_point disconnect_time_;  // 断线时间
        std::chrono::steady_clock::time_point reconnect_start_time_; // 重连开始时间
        
        std::vector<uint8_t> send_buffer_;
        std::vector<uint8_t> receive_buffer_;
        std::vector<uint8_t> business_data_buffer_;      // 业务数据缓冲区（复用于网关代理模式）
        std::vector<uint8_t> business_receive_buffer_;  // 业务连接接收缓冲区
        // std::vector<uint8_t> tunnel_heartbeat_buffer_;  // D-2 隧道心跳缓冲区（已移除）
        // std::vector<uint8_t> tunnel_data_buffer_;       // D-2 隧道数据缓冲区（已移除）
        
        // 控制消息读取缓冲区
        std::array<uint8_t, 16> cmd_header_buf_;     // 固定16字节命令头
        std::vector<uint8_t> recv_body_buf_;         // 可变长度消息体
        
        std::chrono::steady_clock::time_point start_time_;
        std::chrono::steady_clock::time_point last_active_send_;
        std::chrono::steady_clock::time_point last_active_recv_;
        // === Diagnostics ===
        std::chrono::steady_clock::time_point ts_connect_ok_;  // TS0: socket 连接完成
        std::chrono::steady_clock::time_point ts_ctrl_ok_;     // TS1: 控制 LOGIN 成功
        uint64_t rtt_estimate_us_ = 0;             // 最近一次RTT估算(μs)
        // 新增：剩余握手重试次数
        uint32_t handshake_retry_left_ = 0;
        uint32_t external_ip_ = 0;          // 外网IP地址
        LoginResult login_result_;

        // === 新增：心跳监控 ===
        std::chrono::steady_clock::time_point last_heartbeat_ts_{};
        bool heartbeat_monitor_started_ = false;

        bool control_connection_established_ = false;   // 控制连接已建立标志
        // 在业务/控制socket之后添加数据隧道对象（已关闭）
        // std::shared_ptr<TunnelChannel> tunnel_channel_; // 隧道功能已彻底关闭

        // Diagnostics: count connect attempts
        int control_connect_attempt_{0};
        int business_connect_attempt_{0};

        // === New: data tunnel retry state ===
        bool tunnel_inflight_{false};           // true if a tunnel connect/handshake is in progress
        int  tunnel_backoff_ms_{100};           // exponential backoff delay, start 100ms, max 8s
        // === Heartbeat diagnostics ===
        uint32_t hb_sent_ = 0;
        uint32_t hb_ack_  = 0;

        // === 写入队列管理 ===
        std::atomic<int> pending_writes_{0};     // 当前待完成的写入操作数
        static constexpr int MAX_PENDING_WRITES = 5;  // 最大待写入队列长度
    };

private:
    // 内部方法
    void schedule_next_handshake();
    void create_user_session();
    uint32_t get_next_user_id();
    void update_stats_on_completion(bool success, uint64_t latency_us, size_t bytes_sent, size_t bytes_received);

    // 🔧 性能优化：批处理方法
    void start_global_timers();
    void stop_global_timers();
    void process_heartbeat_batch();
    void process_business_data_batch();
    void schedule_heartbeat_batch();
    void schedule_business_data_batch();
    
private:
    boost::asio::io_context& io_context_;
    std::deque<std::shared_ptr<UserSession>> connections_;    // 所有用户会话（deque 避免 reallocate 失效）
    mutable std::mutex connections_mutex_;
    
    MiniBoosterConfig config_;
    HandshakeCallback callback_;
    std::atomic<bool> running_;
    std::atomic<uint32_t> current_user_id_;
    
    boost::asio::deadline_timer handshake_timer_;

    // 🔧 性能优化：全局批处理定时器
    boost::asio::deadline_timer global_heartbeat_timer_;     // 全局心跳批处理定时器
    boost::asio::deadline_timer global_business_timer_;     // 全局业务数据批处理定时器

    // 批处理控制
    std::atomic<bool> batch_processing_enabled_{true};      // 是否启用批处理模式
    std::atomic<size_t> heartbeat_batch_index_{0};          // 心跳批处理索引
    std::atomic<size_t> business_batch_index_{0};           // 业务数据批处理索引

    mutable std::mutex stats_mutex_;
    MiniBoosterStats stats_;
};

} // namespace mini_booster 