#include "../include/console_controller.h"
#include "../include/config/mini_booster_config_loader.h"
#include "../include/error_logger.h"
#include "../include/performance_monitor.h"
#include "platform_utils.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <ctime>
#include <streambuf>
#include <mutex>

#ifdef _WIN32
#include <windows.h>
#endif

// ============ P4-C: NullBuffer 定义 ============
class NullBuffer : public std::streambuf {
protected:
    int_type overflow(int_type c) override { return traits_type::not_eof(c); }
};

namespace mini_booster {

static std::mutex cout_mtx;  // 全局输出互斥

// P2-A-2阶段：实现配置文件加载功能

// 复用gateway-load-tester的parseCommand函数
std::vector<std::string> ConsoleController::parse_command(const std::string& input) {
    std::vector<std::string> tokens;
    std::istringstream iss(input);
    std::string token;
    while (iss >> token) {
        tokens.push_back(token);
    }
    return tokens;
}

ConsoleController::ConsoleController(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , session_(nullptr)
    , metrics_aggregator_(nullptr)
    , running_(false)
    , reconnect_test_timer_(io_context)
    , memory_monitor_timer_(std::make_unique<boost::asio::steady_timer>(io_context)) {
    
    // 设置控制台编码为UTF-8（Windows）
#ifdef _WIN32
    SetConsoleOutputCP(65001);
    SetConsoleCP(65001);
#endif
    
    // 加载配置文件
    if (!load_config()) {
        std::cout << "⚠️ 使用默认配置，配置文件加载失败" << std::endl;
    }
    
    // 创建会话实例
    session_ = std::make_unique<MiniBoosterSession>(io_context_);

    // 🔧 修复：配置会话实例，避免使用结构体默认值
    session_->configure(config_);
    
    // P2-C-1: 创建指标聚合器
    metrics_aggregator_ = std::make_unique<MetricsAggregator>();
    
    // P2-C-2: 创建报告生成器
    report_generator_ = std::make_unique<ReportGenerator>();
    report_generator_->setDefaultOutputDirectory("reports");
    
    // P3-A: 创建连接压测实例
    stress_test_ = std::make_unique<ConnectionStressTest>(io_context_);
    
    // P3-B: 创建隧道压测实例
    tunnel_test_ = std::make_unique<TunnelStressTest>(io_context_);
    
    // P3-C: 创建多应用隔离测试实例
    multi_app_test_ = std::make_unique<MultiAppIsolationTest>(io_context_);
    
    // P5-B: 创建自动极限压测实例
    auto_stress_runner_ = std::make_unique<AutoStressRunner>(io_context_);
    
    std::cout << "✅ Mini-Booster Console Controller 初始化完成" << std::endl;

    // P4-C: 日志级别初始化
    cout_buf_backup_ = std::cout.rdbuf();    // 备份原始缓冲区
    verbose_mode_ = false;                   // 默认关闭详细日志
    // 如果关闭详细日志，则重定向cout到空缓冲区
    if(!verbose_mode_) {
        static NullBuffer null_buffer_instance;
        std::cout.rdbuf(&null_buffer_instance);
    }
    
    // 始终启用文件详细日志
    ErrorLogger::instance().set_verbose(true);
}

ConsoleController::~ConsoleController() {
    stop();
}

void ConsoleController::run() {
    running_.store(true);
    static NullBuffer null_buffer_instance;
    std::ostream real_out(cout_buf_backup_);

    // 始终保持静默缓冲
    if(!verbose_mode_) {
        std::cout.rdbuf(&null_buffer_instance);
    }

    // 使用 real_out 直接打印横幅和帮助（避免更改函数签名）
    real_out << "\n==========================================" << std::endl;
    real_out << "🚀 Mini-Booster 压力测试工具" << std::endl;
    real_out << "   轻量、可控、高效的网关压力测试解决方案" << std::endl;
    real_out << "   基于 CcBoosterGate 深度分析的专业测试工具" << std::endl;
    real_out << "==========================================" << std::endl;

    // 简要帮助
    real_out << "\n📖 可用命令(输入 help 查看完整列表)" << std::endl;
    real_out << "  start / stop / status / target N / scale up N / scale down N" << std::endl;
    real_out << "  quit" << std::endl;

    real_out << "\n💡 输入命令开始操作（例如：help, status, start, quit）" << std::endl;
    
    // 主控制循环 - 复用gateway-load-tester的逻辑
    while (running_.load()) {
        if(verbose_mode_) {
            std::cout << "\n[Mini-Booster] > ";
        } else {
            real_out << "\n[Mini-Booster] > " << std::flush;
        }

        std::string input;
        std::getline(std::cin, input);
        
        if (!input.empty()) {
            if (!execute_command(input)) {
                break;  // 用户选择退出
            }
        }
    }
    
    real_out << "\nMini-Booster Console 已退出" << std::endl;
    // 记录控制台退出日志
    ErrorLogger::instance().log_info("Mini-Booster Console 已退出");
}

void ConsoleController::stop() {
    if (!running_.load()) {
        return;
    }
    
    ErrorLogger::instance().log_info("正在停止Mini-Booster...");
    running_.store(false);
    
    // 取消任何仍在等待的重连测试计时器
    boost::system::error_code ignore_ec;
    reconnect_test_timer_.cancel(ignore_ec);
    
    if (session_ && session_->is_running()) {
        session_->stop();
    }
    
    stop_memory_monitor();
    ErrorLogger::instance().log_info("Mini-Booster已停止");
}

bool ConsoleController::is_running() const {
    return running_.load();
}

// 复用gateway-load-tester的命令执行逻辑（简化版）
bool ConsoleController::execute_command(const std::string& input) {
    static NullBuffer null_buffer_instance;
    std::ostream real_out(cout_buf_backup_);

    // 解析命令
    auto tokens = parse_command(input);
    if (tokens.empty()) {
        return true;
    }

    // 统一输出流
    auto& UI = verbose_mode_ ? std::cout : real_out;

    const std::string& cmd = tokens[0];
    
    if (cmd == "help") {
        show_help();
    }
    else if (cmd == "status") {
        show_status();
    }
    else if (cmd == "perf") {
        PerformanceMonitor::instance().print_stats();
    }
    else if (cmd == "verbose" || cmd == "debug") {
        if(tokens.size() < 2) {
            std::cout << "❌ 用法: verbose <on/off>" << std::endl;
        } else {
            const std::string& arg = tokens[1];
            if(arg == "on") {
                set_verbose(true);
            } else if(arg == "off") {
                set_verbose(false);
            } else {
                std::cout << "❌ 参数错误, 使用 on 或 off" << std::endl;
            }
        }
    }
    else if (cmd == "start") {
        if (session_ && !session_->is_running()) {
            std::cout << "�� 启动 Mini-Booster 测试..." << std::endl;
            
            // 配置会话（使用新的配置结构）
            MiniBoosterConfig session_config = config_;  // 直接使用当前配置
            
            session_->configure(session_config);
            
            if (session_->start()) {
                std::cout << "✅ Mini-Booster 启动成功！" << std::endl;
                std::cout << "  输入 'status' 查看状态，'stop' 停止测试" << std::endl;
            } else {
                std::cout << "❌ Mini-Booster 启动失败" << std::endl;
            }
        } else {
            std::cout << "⚠️ 测试已在运行中" << std::endl;
        }
    }
    else if (cmd == "stop") {
        bool any_stopped = false;
        
        // 停止自动极限压测
        if (auto_stress_runner_ && auto_stress_runner_->is_running()) {
            std::cout << "🛑 停止自动极限压测..." << std::endl;
            auto_stress_runner_->stop();
            std::cout << "✅ 自动极限压测已停止" << std::endl;
            any_stopped = true;
        }
        
        // 停止高并发优化压测
        if (high_concurrency_runner_ && high_concurrency_runner_->is_running()) {
            std::cout << "🛑 停止高并发优化压测..." << std::endl;
            high_concurrency_runner_->stop();
            std::cout << "✅ 高并发优化压测已停止" << std::endl;
            any_stopped = true;
        }
        
        // 停止基础会话测试
        if (session_ && session_->is_running()) {
            std::cout << "🛑 停止 Mini-Booster 测试..." << std::endl;
            session_->stop();
            std::cout << "✅ 测试已停止" << std::endl;
            stop_memory_monitor();
            any_stopped = true;
        }
        
        if (!any_stopped) {
            std::cout << "⚠️ 当前没有运行的测试" << std::endl;
        }
    }
    else if (cmd == "set") {
        if (tokens.size() < 3) {
            std::cout << "❌ 用法: set <参数> <值>" << std::endl;
            std::cout << "💡 可用参数: users, pps, duration, gateway, node" << std::endl;
            return true;
        }
        
        const std::string& param = tokens[1];
        const std::string& value = tokens[2];
        
        if (set_config_value(param, value)) {
            std::cout << "✅ 参数 " << param << " 已设置为: " << value << std::endl;
        } else {
            std::cout << "❌ 设置参数失败: " << param << " = " << value << std::endl;
        }
    }
    else if (cmd == "scale") {
        // P2-B-2: 动态扩缩容功能
        if (tokens.size() < 3) {
            std::cout << "❌ 用法: scale <up/down> <数量>" << std::endl;
            std::cout << "💡 示例: scale up 200, scale down 50" << std::endl;
            return true;
        }
        
        const std::string& direction = tokens[1];
        const std::string& count_str = tokens[2];
        
        try {
            int count = std::stoi(count_str);
            if (count <= 0) {
                std::cout << "❌ 数量必须大于0" << std::endl;
                return true;
            }
            
            if (direction == "up") {
                scale_connections_up(count);
            } else if (direction == "down") {
                scale_connections_down(count);
            } else {
                std::cout << "❌ 方向参数错误，请使用 up 或 down" << std::endl;
            }
        } catch (const std::exception&) {
            std::cout << "❌ 数量参数无效: " << count_str << std::endl;
        }
    }
    else if (cmd == "target") {
        // P2-B-2: 设置目标用户数功能
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: target <用户数>" << std::endl;
            std::cout << "💡 示例: target 1000" << std::endl;
            return true;
        }
        
        try {
            int target_users = std::stoi(tokens[1]);
            if (target_users <= 0) {
                std::cout << "❌ 目标用户数必须大于0" << std::endl;
                return true;
            }
            
            set_target_users(target_users);
        } catch (const std::exception&) {
            std::cout << "❌ 目标用户数参数无效: " << tokens[1] << std::endl;
        }
    }
    else if (cmd == "export") {
        // P2-C-2: 报告导出功能
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: export <类型> [文件名]" << std::endl;
            std::cout << "💡 可用类型: summary, detailed, json, csv" << std::endl;
            std::cout << "💡 示例: export summary, export json test.json" << std::endl;
            return true;
        }
        
        const std::string& type = tokens[1];
        std::string filename = (tokens.size() > 2) ? tokens[2] : "";
        
        if (type == "summary") {
            generate_summary_report(filename);
        } else if (type == "detailed") {
            generate_detailed_report(filename);
        } else if (type == "json") {
            generate_json_report(filename);
        } else if (type == "csv") {
            generate_csv_report(filename);
        } else {
            std::cout << "❌ 未知的报告类型: " << type << std::endl;
            std::cout << "💡 可用类型: summary, detailed, json, csv" << std::endl;
        }
    }
    else if (cmd == "metrics") {
        // P2-C-1: 显示指标报告
        show_metrics();
    }
    else if (cmd == "stress") {
        // P3-A: 连接极限压测命令
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: stress <max|cps> [参数]" << std::endl;
            std::cout << "   max [连接数]        - 测试最大并发连接数" << std::endl;
            std::cout << "   cps [时长秒]        - 测试每秒建立连接数" << std::endl;
            return true;
        }

        if (!stress_test_) {
            stress_test_ = std::make_unique<ConnectionStressTest>(io_context_);
        }

        const std::string& subcmd = tokens[1];
        if (subcmd == "max") {
            ConnectionStressConfig cfg;
            cfg.host = config_.gateway_host;
            cfg.port = config_.gateway_port;

            if (tokens.size() >= 3) {
                try {
                    cfg.max_connections = static_cast<size_t>(std::stoul(tokens[2]));
                } catch (...) {
                    std::cout << "⚠️ 连接数参数无效" << std::endl;
                    return true;
                }
            } else {
                cfg.max_connections = 1000; // 默认
            }

            stress_test_->configure(cfg);
            stress_test_->start_max_concurrent_test();
        } else if (subcmd == "cps") {
            ConnectionStressConfig cfg;
            cfg.host = config_.gateway_host;
            cfg.port = config_.gateway_port;
            cfg.batch_size = 50;

            if (tokens.size() >= 3) {
                try {
                    cfg.test_duration_seconds = static_cast<size_t>(std::stoul(tokens[2]));
                } catch (...) {
                    std::cout << "⚠️ 时长参数无效" << std::endl;
                    return true;
                }
            }

            stress_test_->configure(cfg);
            stress_test_->start_cps_test();
        } else {
            std::cout << "❌ 未知stress子命令: " << subcmd << std::endl;
        }
    }
    else if (cmd == "tunnel") {
        // P3-B: 业务隧道压测命令
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: tunnel <fullpath|throughput> [参数]" << std::endl;
            std::cout << "   fullpath [用户数] [时长]  - 全链路压测(控制连接+数据隧道)" << std::endl;
            std::cout << "   throughput [用户数] [时长] - 隧道吞吐量测试" << std::endl;
            return true;
        }

        if (!tunnel_test_) {
            tunnel_test_ = std::make_unique<TunnelStressTest>(io_context_);
        }

        const std::string& subcmd = tokens[1];
        if (subcmd == "fullpath") {
            TunnelStressConfig cfg;
            cfg.gateway_host = config_.gateway_host;
            cfg.gateway_port = config_.gateway_port;
            cfg.node_host = config_.node_host;
            cfg.node_port = config_.node_port;
            cfg.app_id = config_.app_id;

            // 解析可选参数
            if (tokens.size() >= 3) {
                try {
                    cfg.concurrent_users = static_cast<size_t>(std::stoul(tokens[2]));
                } catch (...) {
                    std::cout << "⚠️ 用户数参数无效，使用默认值50" << std::endl;
                }
            }
            if (tokens.size() >= 4) {
                try {
                    cfg.test_duration_seconds = static_cast<size_t>(std::stoul(tokens[3]));
                } catch (...) {
                    std::cout << "⚠️ 时长参数无效，使用默认值60秒" << std::endl;
                }
            }

            tunnel_test_->configure(cfg);
            tunnel_test_->start_full_path_stress_test();
        } else if (subcmd == "throughput") {
            TunnelStressConfig cfg;
            cfg.gateway_host = config_.gateway_host;
            cfg.gateway_port = config_.gateway_port;
            cfg.node_host = config_.node_host;
            cfg.node_port = config_.node_port;
            cfg.app_id = config_.app_id;

            // 解析可选参数
            if (tokens.size() >= 3) {
                try {
                    cfg.throughput_test_users = static_cast<size_t>(std::stoul(tokens[2]));
                } catch (...) {
                    std::cout << "⚠️ 用户数参数无效，使用默认值20" << std::endl;
                }
            }
            if (tokens.size() >= 4) {
                try {
                    cfg.throughput_duration_seconds = static_cast<size_t>(std::stoul(tokens[3]));
                } catch (...) {
                    std::cout << "⚠️ 时长参数无效，使用默认值30秒" << std::endl;
                }
            }

            tunnel_test_->configure(cfg);
            tunnel_test_->start_tunnel_throughput_test();
        } else {
            std::cout << "❌ 未知tunnel子命令: " << subcmd << std::endl;
        }
    }
    else if (cmd == "multiapp") {
        // P3-C: 多应用隔离测试命令
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: multiapp <isolation|port|limits> [参数]" << std::endl;
            std::cout << "   isolation [时长]         - 应用统计隔离测试" << std::endl;
            std::cout << "   port [时长]              - 端口隔离测试" << std::endl;
            std::cout << "   limits [时长]            - 应用连接数限制测试" << std::endl;
            return true;
        }

        // 检查是否启用多应用模式
        if (!config_.multi_app_mode || config_.applications.empty()) {
            std::cout << "❌ 多应用测试需要在配置文件中启用 multi_app.enable: true 并配置应用列表" << std::endl;
            std::cout << "💡 请编辑 mini_booster.yaml 配置文件" << std::endl;
            return true;
        }

        if (!multi_app_test_) {
            multi_app_test_ = std::make_unique<MultiAppIsolationTest>(io_context_);
        }

        const std::string& subcmd = tokens[1];
        
        // 配置多应用测试
        MultiAppIsolationConfig multi_config;
        multi_config.gateway_host = config_.gateway_host;
        multi_config.gateway_port = config_.gateway_port;
        multi_config.applications = config_.applications;
        multi_config.statistics_check_interval_seconds = config_.app_statistics_check_interval_seconds;
        multi_config.packets_per_second = config_.packets_per_second;
        multi_config.packet_size = config_.packet_size;

        // 解析可选的测试时长参数
        if (tokens.size() >= 3) {
            try {
                multi_config.test_duration_seconds = static_cast<uint32_t>(std::stoul(tokens[2]));
            } catch (...) {
                std::cout << "⚠️ 时长参数无效，使用默认值60秒" << std::endl;
                multi_config.test_duration_seconds = 60;
            }
        } else {
            multi_config.test_duration_seconds = 60;  // 默认60秒
        }

        multi_app_test_->configure(multi_config);

        if (subcmd == "isolation") {
            std::cout << "🚀 启动应用统计隔离测试..." << std::endl;
            std::cout << "📊 测试应用: " << config_.applications.size() << " 个" << std::endl;
            std::cout << "⏱️ 测试时长: " << multi_config.test_duration_seconds << " 秒" << std::endl;
            
            multi_app_test_->start_app_statistics_isolation_test();
        } else if (subcmd == "port") {
            std::cout << "🚀 启动端口隔离测试..." << std::endl;
            multi_app_test_->start_port_isolation_test();
        } else if (subcmd == "limits") {
            std::cout << "🚀 启动应用连接数限制测试..." << std::endl;
            multi_app_test_->start_app_connection_limits_test();
        } else {
            std::cout << "❌ 未知multiapp子命令: " << subcmd << std::endl;
        }
    }
    else if (cmd == "reconnect") {
        // P4-A: 重连测试命令
        if (tokens.size() < 2) {
            std::cout << "❌ 用法: reconnect <test|config|simulate>" << std::endl;
            std::cout << "   test [用户数] [时长]      - 重连稳定性测试" << std::endl;
            std::cout << "   config [参数] [值]        - 配置重连参数" << std::endl;
            std::cout << "   simulate [用户ID]         - 手动触发指定用户重连" << std::endl;
            return true;
        }
        
        const std::string& subcmd = tokens[1];
        if (subcmd == "test") {
            start_reconnect_test(tokens);
        } else if (subcmd == "config") {
            configure_reconnect_settings(tokens);
        } else if (subcmd == "simulate") {
            simulate_user_disconnect(tokens);
        } else {
            std::cout << "❌ 未知reconnect子命令: " << subcmd << std::endl;
        }
    }
    else if (cmd == "stability") {
        run_stability_test(tokens);
    }
    else if (cmd == "autostress") {
        // P5-D: 自动极限压测命令
        run_autostress_test(tokens);
    }
    else if (cmd == "highstress") {
        // 高并发优化压测命令
        run_highstress_test(tokens);
    }
    else if (cmd == "quit" || cmd == "exit") {
        std::cout << "正在退出 Mini-Booster..." << std::endl;
        return false;  // 退出主循环
    }
    else {
        std::cout << "❌ 未知命令: " << cmd << std::endl;
        std::cout << "💡 输入 'help' 查看可用命令" << std::endl;
    }

    return true;  // 继续运行
}

void ConsoleController::show_banner() {}

void ConsoleController::show_help() {
    std::ostream real_out(cout_buf_backup_);
    auto& out = verbose_mode_ ? std::cout : real_out;
    
    out << "\n📖 Mini-Booster 命令帮助:" << std::endl;
    out << "\n🔧 基本控制:" << std::endl;
    out << "  start                    - 启动基础压力测试" << std::endl;
    out << "  stop                     - 停止当前测试" << std::endl;
    out << "  status                   - 显示当前状态" << std::endl;
    out << "  perf                     - 显示性能统计" << std::endl;
    out << "  set <key> <value>        - 设置配置参数" << std::endl;
    out << "  verbose <on/off>         - 开关详细日志" << std::endl;
    
    out << "\n📊 动态控制:" << std::endl;
    out << "  target <N>               - 设置目标用户数" << std::endl;
    out << "  scale up <N>             - 增加N个连接" << std::endl;
    out << "  scale down <N>           - 减少N个连接" << std::endl;
    
    out << "\n🚀 高级测试:" << std::endl;
    out << "  stress max [N]           - 连接极限压测" << std::endl;
    out << "  stress cps [S]           - CPS压测" << std::endl;
    out << "  tunnel fullpath [N] [S]  - 全链路压测" << std::endl;
    out << "  tunnel throughput [N] [S] - 隧道吞吐量测试" << std::endl;
    out << "  multiapp isolation [S]   - 多应用隔离测试" << std::endl;
    out << "  reconnect test [N] [S]   - 重连稳定性测试" << std::endl;
    out << "  stability [minutes]      - 长期稳定性测试" << std::endl;
    out << "  autostress [batch] [interval] [threshold] [max] - 🎯 自动极限压测" << std::endl;
    out << "  highstress [connections] [shards]              - ⚡ 高并发优化压测" << std::endl;
    
    out << "\n📈 报告与指标:" << std::endl;
    out << "  metrics                  - 显示性能指标" << std::endl;
    out << "  export <type> [filename] - 导出报告 (summary/detailed/json/csv)" << std::endl;
    
    out << "\n❓ 其他:" << std::endl;
    out << "  help                     - 显示此帮助信息" << std::endl;
    out << "  quit / exit              - 退出程序" << std::endl;
    
    out << "\n💡 示例:" << std::endl;
    out << "  autostress               - 使用默认配置启动自动极限压测" << std::endl;
    out << "  autostress 50 3 8.0      - 每批次50个连接，间隔3秒，阈值8%" << std::endl;
    out << "  autostress help          - 查看autostress详细帮助" << std::endl;
    out << "  highstress help          - 查看highstress详细帮助" << std::endl;
}

void ConsoleController::show_status() {
    std::ostream real_out(cout_buf_backup_);
    auto& out = verbose_mode_ ? std::cout : real_out;

    out << "\n📊 当前状态:" << std::endl;
    // show_config 使用 std::cout，目前简单重定向
    std::streambuf* prev = nullptr;
    if (!verbose_mode_) {
        prev = std::cout.rdbuf(cout_buf_backup_);
    }
    show_config();
    if (!verbose_mode_) {
        std::cout.rdbuf(prev);
    }
    
    // P4-A 显示重连配置
    out << "  重连配置:" << std::endl;
    out << "    启用状态: " << (config_.reconnect_config.enabled ? "✅ 已启用" : "❌ 已禁用") << std::endl;
    out << "    最大尝试: " << config_.reconnect_config.max_attempts << " 次" << std::endl;
    out << "    基础延迟: " << config_.reconnect_config.base_delay_ms << " ms" << std::endl;
    out << "    最大延迟: " << config_.reconnect_config.max_delay_ms << " ms" << std::endl;
    out << "    断线概率: " << (config_.reconnect_config.failure_probability * 100) << "%" << std::endl;
    out << "    心跳阈值: " << config_.reconnect_config.heartbeat_timeout_threshold << " 次" << std::endl;
    
    if (session_) {
        if (session_->is_running()) {
            out << "  运行状态: ✅ 正在运行" << std::endl;
            session_->print_status();
            
            // P4-A 显示重连统计
            auto stats = session_->get_stats();
            if (stats.reconnect_attempts > 0) {
                out << "  重连统计:" << std::endl;
                out << "    尝试次数: " << stats.reconnect_attempts << std::endl;
                out << "    成功次数: " << stats.reconnect_successes << std::endl;
                out << "    失败次数: " << stats.reconnect_failures << std::endl;
                out << "    成功率: " << (stats.reconnect_attempts > 0 ? 
                    (stats.reconnect_successes * 100.0 / stats.reconnect_attempts) : 0) << "%" << std::endl;
                
                if (stats.reconnect_successes > 0) {
                    double avg_time = stats.total_reconnect_time_ms / static_cast<double>(stats.reconnect_successes);
                    out << "    平均重连时间: " << avg_time << " ms" << std::endl;
                    out << "    最快重连: " << stats.min_reconnect_time_ms << " ms" << std::endl;
                    out << "    最慢重连: " << stats.max_reconnect_time_ms << " ms" << std::endl;
                }
                
                out << "    心跳超时: " << stats.heartbeat_timeouts << " 次" << std::endl;
                out << "    网络错误: " << stats.network_errors << " 次" << std::endl;
            }
        } else {
            out << "  运行状态: ⏸️ 已停止" << std::endl;
        }
    } else {
        out << "  运行状态: ❌ 未初始化" << std::endl;
    }
    
    // P5-D: 显示自动极限压测状态
    if (auto_stress_runner_ && auto_stress_runner_->is_running()) {
        out << "\n🎯 自动极限压测状态:" << std::endl;
        auto current_batch = auto_stress_runner_->get_current_batch();
        out << "  当前批次: " << current_batch.batch_number << std::endl;
        out << "  总用户数: " << current_batch.total_users << std::endl;
        out << "  成功连接: " << current_batch.successful_connections << std::endl;
        out << "  失败连接: " << current_batch.failed_connections << std::endl;
        out << "  失败率: " << current_batch.failure_rate_percent << "%" << std::endl;
        out << "  当前CPS: " << current_batch.connections_per_second << "/s" << std::endl;
    }
}

// P2-C-1: 指标聚合器集成 - 显示性能指标
void ConsoleController::show_metrics() {
    if (!metrics_aggregator_) {
        std::cout << "❌ 指标聚合器未初始化" << std::endl;
        return;
    }
    
    if (session_ && session_->is_running()) {
        // 从会话收集最新指标
        metrics_aggregator_->CollectFromSession("main_session", *session_);
    }
    
    // 显示聚合指标报告
    auto report_str = "\n" + metrics_aggregator_->GetFormattedReport(true);
    std::cout << report_str << std::endl;
    ErrorLogger::instance().log_info("METRICS_SNAPSHOT\n" + report_str);
}

// 配置管理方法实现 - P2-A-2阶段：实现配置文件加载
bool ConsoleController::load_config(const std::string& config_file) {
    MiniBoosterConfigLoader loader;
    
    // 尝试加载配置文件
    if (loader.loadFromFile(config_file)) {
        config_ = loader.getConfig();
        std::cout << "✅ 配置文件加载成功: " << config_file << std::endl;
        return true;
    } else {
        // 配置文件加载失败，使用默认配置
        std::cout << "⚠️ 配置文件加载失败: " << loader.getLastError() << std::endl;
        std::cout << "🔄 使用默认配置..." << std::endl;
        
        // 设置默认配置（与YAML文件中的默认值保持一致）
        config_.gateway_host = "***************";
        config_.gateway_port = 5188;
        config_.node_host = "***************";
        config_.node_port = 5188;
        config_.app_id = "8888";
        config_.start_user_id = 100000;
        config_.users_count = 0;  // 🔧 修复：默认不启动任何用户，避免自动执行
        config_.packets_per_second = 3;  // 🔧 降低发送频率，减少CPU负载
        config_.packet_size = 1024;
        config_.test_duration_seconds = 300;
        config_.enable_heartbeat = true;
        config_.heartbeat_interval_seconds = 5;  // 🔧 增加心跳间隔，减少网络负载
        config_.business_data_interval_ms = 50;  // 从100毫秒改为50毫秒，更频繁发送业务数据
        config_.enable_data_tunnel = true;
        config_.tunnel_heartbeat_interval_ms = 5000;
        config_.close_control_after_handshake = false;
        
        std::cout << "✅ 默认配置已设置" << std::endl;
        return true;
    }
}

void ConsoleController::show_config() {
    std::cout << "  网关地址: " << config_.gateway_host << ":" << config_.gateway_port << std::endl;
    std::cout << "  节点地址: " << config_.node_host << ":" << config_.node_port << std::endl;
    std::cout << "  应用ID: " << config_.app_id << std::endl;
    std::cout << "  用户数: " << config_.users_count << " 个" << std::endl;
    std::cout << "  角色: " << (config_.role_id == 0 ? "机器A" : "机器B") << " (UserID: "
              << (config_.start_user_id + config_.role_id * 100000) << "+)" << std::endl;
    std::cout << "  发包速率: " << config_.packets_per_second << " 包/秒" << std::endl;
    std::cout << "  包大小: " << config_.packet_size << " 字节" << std::endl;
    std::cout << "  测试时长: " << config_.test_duration_seconds << " 秒" << std::endl;
}

bool ConsoleController::set_config_value(const std::string& key, const std::string& value) {
    try {
        if (key == "users") {
            config_.users_count = std::stoul(value);
            return true;
        }
        else if (key == "pps") {
            config_.packets_per_second = std::stoul(value);
            return true;
        }
        else if (key == "duration") {
            config_.test_duration_seconds = std::stoul(value);
            return true;
        }
        else if (key == "packet_size") {
            config_.packet_size = std::stoul(value);
            return true;
        }
        else if (key == "node_host") {
            config_.node_host = value;
            return true;
        }
        else if (key == "node_port") {
            config_.node_port = static_cast<uint16_t>(std::stoul(value));
            return true;
        }
        else if (key == "gateway_host") {
            config_.gateway_host = value;
            return true;
        }
        else if (key == "gateway_port") {
            config_.gateway_port = static_cast<uint16_t>(std::stoul(value));
            return true;
        }
        else if (key == "app_id") {
            config_.app_id = value;
            return true;
        }
        else if (key == "role") {
            uint32_t role = std::stoul(value);
            if (role <= 1) {
                config_.role_id = role;
                std::cout << "🏷️  设置为" << (role == 0 ? "机器A" : "机器B") << "，UserID范围: "
                         << (config_.start_user_id + role * 100000) << "-"
                         << (config_.start_user_id + role * 100000 + 999) << std::endl;
                return true;
            } else {
                std::cout << "❌ 角色只能是0(机器A)或1(机器B)" << std::endl;
                return false;
            }
        }
        else {
            std::cout << "❌ 未知参数: " << key << std::endl;
            std::cout << "💡 可用参数: users, pps, duration, packet_size, node_host, node_port, gateway_host, gateway_port, app_id, role" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cout << "❌ 参数值无效: " << value << " (" << e.what() << ")" << std::endl;
        return false;
    }
}

void ConsoleController::set_verbose(bool enable) {
    if(enable == verbose_mode_) {
        // 状态未变化，无需处理
        return;
    }

    static NullBuffer null_buffer_instance;

    if(enable) {
        // 恢复输出
        std::cout.rdbuf(cout_buf_backup_);
        verbose_mode_ = true;
        std::cout << "🔔 Verbose 日志已开启" << std::endl;
        ErrorLogger::instance().set_verbose(true);
        ErrorLogger::instance().log_debug("调试日志已启用");
    } else {
        // 关闭输出
        verbose_mode_ = false;
        // 先向用户确认，然后关闭
        std::cout.rdbuf(cout_buf_backup_);
        std::cout << "🔕 Verbose 日志已关闭" << std::endl;
        ErrorLogger::instance().set_verbose(false);
        std::cout.rdbuf(&null_buffer_instance);
    }
}

// =============== P2-B-2: 动态扩缩容功能实现 ===============

void ConsoleController::scale_connections_up(int count) {
    if (!session_) {
        std::cout << "❌ 会话未初始化" << std::endl;
        return;
    }
    
    if (!session_->is_running()) {
        std::cout << "❌ 测试未运行，请先启动测试" << std::endl;
        return;
    }
    
    if (verbose_mode_) {
        std::cout << "🚀 开始增加 " << count << " 个连接..." << std::endl;
    } else {
        std::ostream real_out(cout_buf_backup_);
        real_out << "🚀 开始增加 " << count << " 个连接..." << std::endl;
    }
    
    // 通过MiniBoosterSession动态添加连接
    int current_users = config_.users_count;
    int new_users = current_users + count;
    
    // 更新配置
    config_.users_count = new_users;

    // 记录扩容操作
    ErrorLogger::instance().log_info("开始增加 " + std::to_string(count) + " 个连接");

    // ==== 静默异步输出：防止后台日志刷屏 =====
    static NullBuffer null_buffer_instance;          // 复用全局静默缓冲
    std::streambuf* previous_buf = nullptr;          // 记录原缓冲区
    if (!verbose_mode_) {
        previous_buf = std::cout.rdbuf();            // 备份当前缓冲区(正常应为 cout_buf_backup_)
        std::cout.rdbuf(&null_buffer_instance);      // 临时静默
    }
    // =========================================
    
    // 通知会话创建新连接
    bool ok = session_->scale_up(count);

    // =====  恢复输出 =====
    if (!verbose_mode_ && previous_buf) {
        std::cout.rdbuf(previous_buf);           // 恢复输出 → 打印提示语
    }
    // =====================
    
    if (verbose_mode_) {
        if (ok) {
            std::cout << "✅ 成功增加连接，当前用户数: " << new_users << std::endl;
        } else {
            std::cout << "❌ 连接增加失败" << std::endl;
        }
    } else {
        std::ostream real_out(cout_buf_backup_);
        if (ok) {
            real_out << "✅ 成功增加连接，当前用户数: " << new_users << std::endl;
        } else {
            real_out << "❌ 连接增加失败" << std::endl;
        }
    }
    if(!ok) {
        config_.users_count = current_users; // 回滚配置
    }
}

void ConsoleController::scale_connections_down(int count) {
    if (!session_) {
        std::cout << "❌ 会话未初始化" << std::endl;
        return;
    }
    
    if (!session_->is_running()) {
        std::cout << "❌ 测试未运行，请先启动测试" << std::endl;
        return;
    }
    
    int current_users = config_.users_count;
    if (count >= current_users) {
        std::cout << "❌ 不能减少超过当前用户数的连接" << std::endl;
        return;
    }
    
    if (verbose_mode_) {
        std::cout << "🛑 开始减少 " << count << " 个连接..." << std::endl;
    } else {
        std::ostream real_out(cout_buf_backup_);
        real_out << "🛑 开始减少 " << count << " 个连接..." << std::endl;
    }
    
    int new_users = current_users - count;
    
    // 更新配置
    config_.users_count = new_users;

    // 记录缩容操作
    ErrorLogger::instance().log_info("开始减少 " + std::to_string(count) + " 个连接");

    // ==== 静默异步输出 =====
    static NullBuffer null_buffer_instance;
    std::streambuf* previous_buf = nullptr;
    if (!verbose_mode_) {
        previous_buf = std::cout.rdbuf();
        std::cout.rdbuf(&null_buffer_instance);
    }
    // =======================
    
    // 通知会话关闭连接
    bool ok = session_->scale_down(count);

    // ===== 恢复输出 =====
    if (!verbose_mode_ && previous_buf) {
        std::cout.rdbuf(previous_buf);           // 恢复输出，打印提示语
    }
    // =====================
    
    if (verbose_mode_) {
        if (ok) {
            std::cout << "✅ 成功减少连接，当前用户数: " << new_users << std::endl;
        } else {
            std::cout << "❌ 连接减少失败" << std::endl;
        }
    } else {
        std::ostream real_out(cout_buf_backup_);
        if (ok) {
            real_out << "✅ 成功减少连接，当前用户数: " << new_users << std::endl;
        } else {
            real_out << "❌ 连接减少失败" << std::endl;
        }
    }
    if(!ok) {
        config_.users_count = current_users; // 回滚配置
    }
}

void ConsoleController::set_target_users(int target_users) {
    if (!session_) {
        std::cout << "❌ 会话未初始化" << std::endl;
        return;
    }
    
    if (!session_->is_running()) {
        std::cout << "❌ 测试未运行，请先启动测试" << std::endl;
        return;
    }
    
    int current_users = config_.users_count;
    int delta = target_users - current_users;
    
    if (delta == 0) {
        std::cout << "💡 当前用户数已经是目标数量: " << target_users << std::endl;
        return;
    }
    
    std::cout << "🎯 设置目标用户数: " << target_users << " (当前: " << current_users << ")" << std::endl;
    
    if (delta > 0) {
        std::cout << "🔄 需要增加 " << delta << " 个连接..." << std::endl;
        scale_connections_up(delta);
    } else {
        std::cout << "🔄 需要减少 " << (-delta) << " 个连接..." << std::endl;
        scale_connections_down(-delta);
    }
}

// =============== P2-C-2: 报告生成功能实现 ===============

void ConsoleController::generate_summary_report(const std::string& filename) {
    if (!metrics_aggregator_) {
        std::cout << "❌ 指标聚合器未初始化" << std::endl;
        return;
    }
    
    if (!report_generator_) {
        std::cout << "❌ 报告生成器未初始化" << std::endl;
        return;
    }
    
    std::cout << "📊 生成摘要报告..." << std::endl;
    
    // 如果会话正在运行，先收集最新指标
    if (session_ && session_->is_running()) {
        metrics_aggregator_->CollectFromSession("main_session", *session_);
    }
    
    auto result = report_generator_->generateSummaryReport(*metrics_aggregator_, filename);
    std::cout << "   " << result.getSummary() << std::endl;
}

void ConsoleController::generate_detailed_report(const std::string& filename) {
    if (!metrics_aggregator_) {
        std::cout << "❌ 指标聚合器未初始化" << std::endl;
        return;
    }
    
    if (!report_generator_) {
        std::cout << "❌ 报告生成器未初始化" << std::endl;
        return;
    }
    
    std::cout << "📋 生成详细报告..." << std::endl;
    
    // 如果会话正在运行，先收集最新指标
    if (session_ && session_->is_running()) {
        metrics_aggregator_->CollectFromSession("main_session", *session_);
    }
    
    auto result = report_generator_->generateDetailedReport(*metrics_aggregator_, filename);
    std::cout << "   " << result.getSummary() << std::endl;
}

void ConsoleController::generate_json_report(const std::string& filename) {
    if (!metrics_aggregator_) {
        std::cout << "❌ 指标聚合器未初始化" << std::endl;
        return;
    }
    
    if (!report_generator_) {
        std::cout << "❌ 报告生成器未初始化" << std::endl;
        return;
    }
    
    std::cout << "🔗 生成JSON报告..." << std::endl;
    
    // 如果会话正在运行，先收集最新指标
    if (session_ && session_->is_running()) {
        metrics_aggregator_->CollectFromSession("main_session", *session_);
    }
    
    ReportConfig config;
    config.type = ReportType::JSON;
    config.output_file = filename;
    config.title = "Mini-Booster JSON Report";
    
    auto result = report_generator_->generateJSONReport(*metrics_aggregator_, config);
    std::cout << "   " << result.getSummary() << std::endl;
}

void ConsoleController::generate_csv_report(const std::string& filename) {
    if (!metrics_aggregator_) {
        std::cout << "❌ 指标聚合器未初始化" << std::endl;
        return;
    }
    
    if (!report_generator_) {
        std::cout << "❌ 报告生成器未初始化" << std::endl;
        return;
    }
    
    std::cout << "📄 生成CSV报告..." << std::endl;
    
    // 如果会话正在运行，先收集最新指标
    if (session_ && session_->is_running()) {
        metrics_aggregator_->CollectFromSession("main_session", *session_);
    }
    
    ReportConfig config;
    config.type = ReportType::CSV;
    config.output_file = filename;
    config.title = "Mini-Booster CSV Report";
    
    auto result = report_generator_->generateCSVReport(*metrics_aggregator_, config);
    std::cout << "   " << result.getSummary() << std::endl;
}

// ===================== P4-A 重连测试方法实现 =====================

void ConsoleController::start_reconnect_test(const std::vector<std::string>& tokens) {
    uint32_t users = 50;      // 默认50个用户
    uint32_t duration = 120;  // 默认2分钟
    
    // 解析参数
    if (tokens.size() >= 3) {
        try {
            users = static_cast<uint32_t>(std::stoul(tokens[2]));
        } catch (...) {
            std::cout << "⚠️ 用户数参数无效，使用默认值50" << std::endl;
        }
    }
    if (tokens.size() >= 4) {
        try {
            duration = static_cast<uint32_t>(std::stoul(tokens[3]));
        } catch (...) {
            std::cout << "⚠️ 时长参数无效，使用默认值120秒" << std::endl;
        }
    }
    
    std::cout << "🔄 启动重连稳定性测试..." << std::endl;
    std::cout << "   用户数: " << users << std::endl;
    std::cout << "   测试时长: " << duration << "秒" << std::endl;
    std::cout << "   重连配置: 最大" << config_.reconnect_config.max_attempts 
              << "次尝试，基础延迟" << config_.reconnect_config.base_delay_ms << "ms" << std::endl;
    
    // 配置测试参数
    auto old_users = config_.users_count;
    auto old_duration = config_.test_duration_seconds;
    auto old_failure_probability = config_.reconnect_config.failure_probability;
    
    config_.users_count = users;
    config_.test_duration_seconds = duration;
    config_.reconnect_config.enabled = true;
    config_.reconnect_config.failure_probability = 0.1; // 10%概率模拟断线
    
    if (session_) {
        session_->configure(config_);
        
        if (!session_->start()) {
            std::cout << "❌ 启动重连测试失败" << std::endl;
            return;
        }
        
        std::cout << "✅ 重连稳定性测试已启动" << std::endl;
        std::cout << "💡 提示: 使用 'status' 命令查看重连统计" << std::endl;
        std::cout << "💡 提示: 使用 'reconnect simulate <用户ID>' 手动触发重连" << std::endl;

        // ================== 新增：自动停止计时器 ==================
        try {
            reconnect_test_timer_.cancel(); // 取消可能存在的旧计时器
            reconnect_test_timer_.expires_from_now(boost::posix_time::seconds(duration));
            reconnect_test_timer_.async_wait([this](const boost::system::error_code& ec) {
                if (!ec) {
                    if (session_ && session_->is_running()) {
                        // 在停止前收集一次指标
                        if (metrics_aggregator_) {
                            metrics_aggregator_->CollectFromSession("main_session", *session_);
                        }

                        // 停止会话
                        session_->stop();

                        // 生成摘要报告并打印指标
                        if (report_generator_ && metrics_aggregator_) {
                            auto result = report_generator_->generateSummaryReport(*metrics_aggregator_, "");
                            std::cout << "📄 自动生成报告: " << result.output_file << std::endl;
                        }

                        // 在控制台显示一次汇总指标
                        show_metrics();

                        std::cout << "✅ 重连测试已自动结束" << std::endl;
                    }
                }
            });
        } catch (const std::exception& e) {
            std::cout << "⚠️ 设置自动结束计时器失败: " << e.what() << std::endl;
        }
    } else {
        std::cout << "❌ 会话未初始化" << std::endl;
    }
    
    // 恢复原始配置
    config_.users_count = old_users;
    config_.test_duration_seconds = old_duration;
    config_.reconnect_config.failure_probability = old_failure_probability;
}

void ConsoleController::configure_reconnect_settings(const std::vector<std::string>& tokens) {
    if (tokens.size() < 4) {
        std::cout << "❌ 用法: reconnect config <参数> <值>" << std::endl;
        std::cout << "💡 可用参数:" << std::endl;
        std::cout << "   enabled <true/false>           - 是否启用重连" << std::endl;
        std::cout << "   max_attempts <数量>            - 最大重连次数" << std::endl;
        std::cout << "   base_delay_ms <毫秒>           - 基础重连延迟" << std::endl;
        std::cout << "   max_delay_ms <毫秒>            - 最大重连延迟" << std::endl;
        std::cout << "   failure_probability <0.0-1.0> - 随机断线概率" << std::endl;
        std::cout << "   heartbeat_threshold <次数>     - 心跳超时阈值" << std::endl;
        return;
    }
    
    const std::string& param = tokens[2];
    const std::string& value = tokens[3];
    
    try {
        if (param == "enabled") {
            config_.reconnect_config.enabled = (value == "true" || value == "1");
            std::cout << "✅ 重连功能已" << (config_.reconnect_config.enabled ? "启用" : "禁用") << std::endl;
        }
        else if (param == "max_attempts") {
            config_.reconnect_config.max_attempts = static_cast<uint32_t>(std::stoul(value));
            std::cout << "✅ 最大重连次数设置为: " << config_.reconnect_config.max_attempts << std::endl;
        }
        else if (param == "base_delay_ms") {
            config_.reconnect_config.base_delay_ms = static_cast<uint32_t>(std::stoul(value));
            std::cout << "✅ 基础重连延迟设置为: " << config_.reconnect_config.base_delay_ms << "ms" << std::endl;
        }
        else if (param == "max_delay_ms") {
            config_.reconnect_config.max_delay_ms = static_cast<uint32_t>(std::stoul(value));
            std::cout << "✅ 最大重连延迟设置为: " << config_.reconnect_config.max_delay_ms << "ms" << std::endl;
        }
        else if (param == "failure_probability") {
            double prob = std::stod(value);
            if (prob < 0.0 || prob > 1.0) {
                std::cout << "❌ 断线概率必须在 0.0-1.0 之间" << std::endl;
                return;
            }
            config_.reconnect_config.failure_probability = prob;
            std::cout << "✅ 随机断线概率设置为: " << (prob * 100) << "%" << std::endl;
        }
        else if (param == "heartbeat_threshold") {
            config_.reconnect_config.heartbeat_timeout_threshold = static_cast<uint32_t>(std::stoul(value));
            std::cout << "✅ 心跳超时阈值设置为: " << config_.reconnect_config.heartbeat_timeout_threshold << "次" << std::endl;
        }
        else {
            std::cout << "❌ 未知参数: " << param << std::endl;
            return;
        }
        
        // 更新会话配置
        if (session_) {
            session_->configure(config_);
            std::cout << "🔧 重连配置已更新到当前会话" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ 设置参数失败: " << e.what() << std::endl;
    }
}

void ConsoleController::simulate_user_disconnect(const std::vector<std::string>& tokens) {
    if (tokens.size() < 3) {
        std::cout << "❌ 用法: reconnect simulate <用户ID>" << std::endl;
        std::cout << "💡 示例: reconnect simulate 100001" << std::endl;
        return;
    }
    
    try {
        uint32_t user_id = static_cast<uint32_t>(std::stoul(tokens[2]));
        
        std::cout << "🎲 模拟用户 " << user_id << " 断线..." << std::endl;
        std::cout << "💡 注意: 该功能需要在会话内部实现具体的断线模拟逻辑" << std::endl;
        
        // TODO: 实现具体的用户断线模拟
        // 这需要在 MiniBoosterSession 中添加一个方法来强制断开指定用户
        
        if (!session_ || !session_->is_running()) {
            std::cout << "❌ 当前没有运行的会话" << std::endl;
            return;
        }
        
        std::cout << "⚠️ 用户断线模拟功能暂未实现，请使用自然网络错误或随机断线进行测试" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "❌ 用户ID参数无效: " << tokens[2] << " (" << e.what() << ")" << std::endl;
    }
}

// ===================== P4-B 长期稳定性测试方法实现 =====================

void ConsoleController::run_stability_test(const std::vector<std::string>& args) {
    if (session_ && session_->is_running()) {
        std::cout << "A test is already running. Please stop it first." << std::endl;
        return;
    }

    long long duration_minutes = 24 * 60; // Default to 24 hours
    if (args.size() > 1) {
        try {
            duration_minutes = std::stoll(args[1]);
        } catch (const std::exception&) {
            std::cout << "Invalid duration. Please provide minutes." << std::endl;
            return;
        }
    }

    std::cout << "Starting stability test for " << duration_minutes << " minutes..." << std::endl;

    if (!ensure_session_running()) {
        return;
    }

    config_.test_duration_seconds = duration_minutes * 60;
    
    if (duration_minutes > 0) {
        try {
            reconnect_test_timer_.cancel();
            reconnect_test_timer_.expires_from_now(boost::posix_time::minutes(duration_minutes));
            reconnect_test_timer_.async_wait([this](const boost::system::error_code& ec) {
                if (!ec) {
                    if (session_ && session_->is_running()) {
                        session_->stop();
                    }
                    stop_memory_monitor();
                    std::cout << "✅ Stability test finished automatically" << std::endl;
                }
            });
        } catch (const std::exception& e) {
            std::cout << "⚠️ Failed to set auto-stop timer: " << e.what() << std::endl;
        }
    }
}

bool ConsoleController::ensure_session_running() {
    if (session_ && session_->is_running()) {
        return true;
    }

    if (!session_) {
        std::cout << "❌ Session object not initialized" << std::endl;
        return false;
    }

    session_->configure(config_);
    if (!session_->start()) {
        std::cout << "❌ Failed to start session – please check gateway address/port or network connectivity" << std::endl;
        return false;
    }

    // 会话成功，启动内存监控
    schedule_memory_monitor();
    memory_monitor_on_.store(true);
    return true;
}

void ConsoleController::schedule_memory_monitor() {
    if (!memory_monitor_timer_) {
        memory_monitor_timer_ = std::make_unique<boost::asio::steady_timer>(io_context_);
    }
    memory_monitor_timer_->expires_after(std::chrono::seconds(15));
    memory_monitor_timer_->async_wait([this](const boost::system::error_code& ec) {
        on_memory_monitor_timer(ec);
    });
}

void ConsoleController::on_memory_monitor_timer(const boost::system::error_code& ec) {
    if (ec) {
        if (ec != boost::asio::error::operation_aborted) {
            std::cerr << "Memory monitor timer error: " << ec.message() << std::endl;
        }
        return;
    }

    uint64_t memory_usage = platform::get_current_process_memory_usage();
    double memory_mb = static_cast<double>(memory_usage) / (1024.0 * 1024.0);

    auto t = std::time(nullptr);
    auto tm = *std::localtime(&t);
    std::cout << "[Stability] " << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") 
              << " - Current Memory Usage: " << std::fixed << std::setprecision(2) << memory_mb << " MB" << std::endl;

    if (memory_monitor_on_.load()) {
        schedule_memory_monitor();
    }
}

void ConsoleController::stop_memory_monitor() {
    if (memory_monitor_timer_) {
        memory_monitor_timer_->cancel();
    }
    memory_monitor_on_.store(false);
}

// ===================== P5-D 自动极限压测方法实现 =====================

void ConsoleController::run_autostress_test(const std::vector<std::string>& args) {
    // 检查是否有其他测试正在运行
    if (auto_stress_runner_ && auto_stress_runner_->is_running()) {
        std::cout << "❌ 自动极限压测已在运行，请先停止" << std::endl;
        std::cout << "💡 使用 'stop' 命令停止当前测试" << std::endl;
        return;
    }
    
    // 检查参数格式，支持可选参数
    if (args.size() > 1 && (args[1] == "help" || args[1] == "?")) {
        std::cout << "🚀 自动极限压测 (autostress) 用法:" << std::endl;
        std::cout << "   autostress                    - 使用默认配置启动" << std::endl;
        std::cout << "   autostress [batch] [interval] [threshold] [max_users]" << std::endl;
        std::cout << "   autostress [batch]            - 只设置批次大小" << std::endl;
        std::cout << "" << std::endl;
        std::cout << "📋 参数说明:" << std::endl;
        std::cout << "   batch       - 每批次增加的连接数 (默认: " << config_.auto_stress_config.batch_size << ")" << std::endl;
        std::cout << "   interval    - 批次间隔秒数 (默认: " << config_.auto_stress_config.interval_seconds << ")" << std::endl;
        std::cout << "   threshold   - 失败率阈值% (默认: " << config_.auto_stress_config.failure_threshold_percent << ")" << std::endl;
        std::cout << "   max_users   - 最大用户数上限 (默认: " << config_.auto_stress_config.max_users << ")" << std::endl;
        std::cout << "" << std::endl;
        std::cout << "💡 示例:" << std::endl;
        std::cout << "   autostress               - 使用配置文件默认值" << std::endl;
        std::cout << "   autostress 50            - 每批次50个连接" << std::endl;
        std::cout << "   autostress 50 3          - 每批次50个，间隔3秒" << std::endl;
        std::cout << "   autostress 50 3 8.0      - 失败率阈值8%" << std::endl;
        std::cout << "   autostress 50 3 8.0 2000 - 最大2000用户" << std::endl;
        return;
    }
    
    // 创建AutoStressRunner实例
    if (!auto_stress_runner_) {
        auto_stress_runner_ = std::make_unique<AutoStressRunner>(io_context_);
    }
    
    // 获取配置并处理命令行参数覆盖
    AutoStressConfig auto_config = config_.auto_stress_config;
    
    try {
        // 解析可选的命令行参数
        if (args.size() > 1) {
            auto_config.batch_size = static_cast<size_t>(std::stoul(args[1]));
        }
        if (args.size() > 2) {
            auto_config.interval_seconds = std::stoul(args[2]);
        }
        if (args.size() > 3) {
            auto_config.failure_threshold_percent = std::stod(args[3]);
        }
        if (args.size() > 4) {
            auto_config.max_users = static_cast<size_t>(std::stoul(args[4]));
        }
        
        // 验证参数范围
        if (auto_config.batch_size == 0) {
            std::cout << "❌ 批次大小不能为0" << std::endl;
            return;
        }
        if (auto_config.failure_threshold_percent < 0.1 || auto_config.failure_threshold_percent > 50.0) {
            std::cout << "❌ 失败率阈值应在 0.1% 到 50% 之间" << std::endl;
            return;
        }
        if (auto_config.max_users > 10000) {
            std::cout << "⚠️ 最大用户数超过10000，可能导致系统过载" << std::endl;
            std::cout << "💡 确认继续请输入 'y'，否则按任意键取消: ";
            std::string confirm;
            std::getline(std::cin, confirm);
            if (confirm != "y" && confirm != "Y") {
                std::cout << "❌ 测试已取消" << std::endl;
                return;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ 参数解析错误: " << e.what() << std::endl;
        std::cout << "💡 使用 'autostress help' 查看正确用法" << std::endl;
        return;
    }
    
    // 配置并启动自动极限压测
    auto_stress_runner_->configure(auto_config, config_.start_user_id, 0);
    
    // 重要修复：确保自动测试使用正确的网关配置和其他关键参数
    auto_stress_runner_->set_gateway_info(config_.gateway_host, config_.gateway_port);
    // 传递节点转发信息，确保业务链路经过Nginx
    auto_stress_runner_->set_node_info(config_.node_host, config_.node_port);
    auto_stress_runner_->set_app_id(config_.app_id);
    
    // 关键修复：确保使用2秒的心跳间隔和适当的保活机制
    auto_stress_runner_->set_heartbeat_interval(2);  // 使用2秒心跳，与修改后的值保持一致

    // 若用户通过 "set pps" 指令修改了发包速率，则将其转换为间隔；否则保持默认 50 ms。
    uint32_t bd_interval_ms = 50; // fallback
    if (config_.packets_per_second > 0) {
        bd_interval_ms = std::max<uint32_t>(1, static_cast<uint32_t>(1000 / config_.packets_per_second));
    }
    auto_stress_runner_->set_business_data_interval(bd_interval_ms);
    
    std::cout << "🎯 准备启动自动极限压测..." << std::endl;
    std::cout << "   网关地址: " << config_.gateway_host << ":" << config_.gateway_port << std::endl;
    std::cout << "   测试参数: batch=" << auto_config.batch_size 
              << " interval=" << auto_config.interval_seconds 
              << "s threshold=" << auto_config.failure_threshold_percent 
              << "% max=" << auto_config.max_users << std::endl;
    std::cout << "   渐进增压: " << (auto_config.enable_gradual_ramp ? "✅ 启用" : "❌ 禁用") << std::endl;
    
    if (!auto_stress_runner_->start()) {
        std::cout << "❌ 自动极限压测启动失败" << std::endl;
        return;
    }
    
    std::cout << "✅ 自动极限压测已启动，使用 'stop' 命令可手动停止" << std::endl;
    std::cout << "📊 实时状态可通过 'status' 命令查看" << std::endl;
}


} // namespace mini_booster