#include "../include/mini_booster_session.h"
#include <iostream>
#include <iomanip>
#include <chrono>
#include <cstring>
#include <random>
#include <atomic>
#include <array>
#include <thread>
#include <boost/asio.hpp>
#include <memory>
#include "../include/error_logger.h"  // 确保包含正确的头文件路径
#include "../include/performance_monitor.h"
#ifdef _MSC_VER
#pragma warning(push)
#pragma warning(disable:4828) // rapidjson 评论中包含 UTF-8 字符导致的噪声告警
#endif
#include "../../../rslib/rapidjson/document.h"
#include "../../../rslib/rapidjson/error/en.h"
#ifdef _MSC_VER
#pragma warning(pop)
#endif
// #include "tunnel_channel.h"  // 隧道功能已关闭
#include "../../include/protocol/pcoi_protocol.h"
#include <fstream>
#include <mutex>
#include <sstream>
#include <algorithm>
#include <psapi.h>
#include <processthreadsapi.h>
#pragma comment(lib, "psapi.lib")

namespace {
// 线程安全地把延迟日志写到文件并同步打印
inline void log_latency_line(const std::string& line) {
    static std::mutex lat_mtx;
    static std::ofstream lat_ofs("latency.log", std::ios::app);
    std::lock_guard<std::mutex> lg(lat_mtx);
    if (lat_ofs.is_open()) {
        lat_ofs << line << std::endl;
    }
    ErrorLogger::instance().log_debug(line);
    std::cout << line << std::endl;
}
}

#ifdef _WIN32
// 抑制常见的Windows编译警告
#pragma warning(push)
#pragma warning(disable: 4996) // 'function': was declared deprecated
#pragma warning(disable: 4065) // switch statement contains 'default' but no 'case' labels
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma warning(pop)
#define strncpy_safe(dest, size, src) strncpy_s(dest, size, src, _TRUNCATE)
#else
#include <arpa/inet.h>
#define strncpy_safe(dest, size, src) strncpy(dest, src, size - 1); dest[size - 1] = '\0'
#endif

namespace mini_booster {

// ===================== UserSession 实现 =====================

MiniBoosterSession::UserSession::UserSession(
    boost::asio::io_context& io_context,
    MiniBoosterSession& mini_booster,
    uint32_t user_id)
    : socket_(io_context)
    , business_socket_(io_context)
    , timeout_timer_(io_context)
    , heartbeat_timer_(io_context)
    , heartbeat_monitor_timer_(io_context)
    , business_data_timer_(io_context)
    // , tunnel_data_timer_(io_context)  // 已移除，使用business_data_timer_
    , business_keepalive_timer_(io_context)
    , reconnect_timer_(io_context)
    , graceful_close_timer_(io_context)
    , jitter_timer_(io_context)
    , mini_booster_(mini_booster)
    , user_id_(user_id)
    , completed_(false)
    , business_session_established_(false)
    , control_connection_closed_(false)
    , session_should_stop_(false)
    , session_state_(SessionState::CONNECTING)
    , reconnect_attempts_(0)
    , saved_user_id_(0)
    , saved_session_id_(generate_session_id())
    , consecutive_heartbeat_failures_(0)
    , receive_buffer_(8192)
    , business_data_buffer_(mini_booster.config_.packet_size)
    , business_receive_buffer_(8192)
    , cmd_header_buf_()
    , recv_body_buf_(8192) {
    
    receive_buffer_.resize(4096);
    business_data_buffer_.resize(1024);  // 默认1KB数据包

    // 初始化心跳监视时间戳
    last_heartbeat_ts_ = std::chrono::steady_clock::now();

    // === 新增：初始化握手重试计数 ===
    handshake_retry_left_ = mini_booster_.config_.handshake_retry;
}

MiniBoosterSession::UserSession::~UserSession() {
    close();
}

void MiniBoosterSession::UserSession::start_handshake() {
    start_time_ = std::chrono::steady_clock::now();

    // === 性能监控：握手开始 ===
    PERF_START("handshake", user_id_);
    PERF_COUNT("handshake_attempts");

    // 记录握手开始日志
    ErrorLogger::instance().log_debug("[HS] start_handshake user=" + std::to_string(user_id_));
    
    // 已移除握手阶段显式超时，统一由心跳监视决定掉线
    
    // 开始异步连接到网关
    try {
        boost::asio::ip::tcp::resolver resolver(socket_.get_executor());
        control_connect_attempt_++;
        std::cout << "[CTL] async_connect attempt #" << control_connect_attempt_ << " user=" << user_id_
                  << " → " << mini_booster_.config_.gateway_host << ":" << mini_booster_.config_.gateway_port << std::endl;
        auto endpoints = resolver.resolve(mini_booster_.config_.gateway_host, 
                                         std::to_string(mini_booster_.config_.gateway_port));
        
        auto self = shared_from_this();
        boost::asio::async_connect(socket_, endpoints,
            [self](const boost::system::error_code& error, const boost::asio::ip::tcp::endpoint&) {
                self->handle_connect(error);
            });
    } catch (const std::exception& e) {
        complete_handshake(false, "Resolve failed: " + std::string(e.what()));
    }

    // 使用异步定时器实现批内抖动，避免阻塞 IO 线程
    auto min_ms = mini_booster_.config_.conn_jitter_min_ms;
    auto max_ms = mini_booster_.config_.conn_jitter_max_ms;
    if (max_ms > 0 && max_ms >= min_ms) {
        static thread_local std::mt19937 rng(std::random_device{}());
        std::uniform_int_distribution<uint32_t> dist(min_ms, max_ms);
        uint32_t delay = dist(rng);

        jitter_timer_.expires_from_now(boost::posix_time::milliseconds(delay));
        auto self = shared_from_this();
        jitter_timer_.async_wait([self](const boost::system::error_code&){ /* 抖动延迟 */ });
    }
}

void MiniBoosterSession::UserSession::handle_connect(const boost::system::error_code& error) {
    if (completed_) return;

    if (error) {
        complete_handshake(false, "Connection failed: " + error.message());
        return;
    }

    // === 优化：设置socket缓冲区大小，减少写入阻塞 ===
    try {
        boost::system::error_code ec;
        // 设置发送缓冲区为64KB，默认通常是8KB
        socket_.set_option(boost::asio::socket_base::send_buffer_size(65536), ec);
        // 设置接收缓冲区为64KB
        socket_.set_option(boost::asio::socket_base::receive_buffer_size(65536), ec);
        // 启用TCP_NODELAY，减少延迟
        socket_.set_option(boost::asio::ip::tcp::no_delay(true), ec);
    } catch (...) {
        // 忽略设置失败，继续连接
    }

    // === TS0: 连接成功 ===
    ts_connect_ok_ = std::chrono::steady_clock::now();
    auto delta_conn_ms = std::chrono::duration_cast<std::chrono::milliseconds>(ts_connect_ok_ - start_time_).count();
    {
        std::ostringstream oss;
        oss << "[LAT] user " << user_id_ << " connect=" << delta_conn_ms << " ms";
        log_latency_line(oss.str());
    }
    
    // 连接成功，发送握手包
    send_handshake_packet();

    // 记录握手成功日志
    ErrorLogger::instance().log_debug("[HS] tcp_connect_ok user=" + std::to_string(user_id_));
}

void MiniBoosterSession::UserSession::send_handshake_packet() {
    if (completed_) return;
    send_game_header();
}

void MiniBoosterSession::UserSession::send_game_header() {
    if (completed_) return;
    
    // 构造 GameHeader - 第一阶段握手包 (D-1: 控制连接)
    GameHeader header = {};
    header.nSignture = CC_SOCK_SIGNTURE;       // 0x12345678
    header.nUserId = 0;                         // 首次登录必须为0，由 RuleServer 分配
    header.nSessionId = saved_session_id_;     // 必须非 0，网关据此建立 Session
    header.nLocalAddress = ntohl(get_local_ip());     // TODO: Replace with real public IP if available
    header.nServerPort = 0;                    // 控制链路必须为 0，让网关分配 51xxx 端口
    header.nReconnect = 0;                     // 首次连接
    header.nListenIp = 0;                      // 控制连接时保持为0
    
    // 设置 AppId
    memset(header.szAppId, 0, sizeof(header.szAppId));
    strncpy_safe(header.szAppId, sizeof(header.szAppId), mini_booster_.config_.app_id.c_str());
    
    // 设置 MAC地址
    memset(header.szMacAddress, 0, sizeof(header.szMacAddress));
    std::string mac = get_mac_address();
    strncpy_safe(header.szMacAddress, sizeof(header.szMacAddress), mac.c_str());
    
    // 之前使用成员 send_buffer_，会在后续异步操作未完成时被 resize 导致迭代器失效。
    // 这里改为为本次发送创建独立的 shared_ptr 缓冲区，确保在回调返回前内存一直有效。
    auto send_buf = std::make_shared<std::vector<uint8_t>>(sizeof(GameHeader));
    memcpy(send_buf->data(), &header, sizeof(GameHeader));
    
    // 调试：打印首包 GameHeader 关键字段
    std::cout << "[DBG] GHDR OUT uid=" << header.nUserId
              << " sid=" << header.nSessionId << std::endl;
    
    // 先启动超时计时器
    timeout_timer_.expires_from_now(boost::posix_time::milliseconds(mini_booster_.config_.handshake_timeout_ms));
    auto self_timeout = shared_from_this();
    timeout_timer_.async_wait([self_timeout](const boost::system::error_code& ec){
        if (!ec) {
            self_timeout->handle_handshake_timeout();
        }
    });
    
    boost::asio::async_write(socket_, boost::asio::buffer(*send_buf),
        [this, send_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            handle_game_header_send(error, bytes_transferred);
        });

    // 记录发送游戏头日志
    ErrorLogger::instance().log_debug("[HS] send_game_header user=" + std::to_string(user_id_));
}

void MiniBoosterSession::UserSession::handle_game_header_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;
    
    if (error) {
        if (handshake_retry_left_ > 0) {
            uint32_t attempt = mini_booster_.config_.handshake_retry - handshake_retry_left_;
            uint32_t delay_ms = 100u * (1u << attempt);              // 100,200,400,800,1600,…
            delay_ms = std::min(delay_ms, 4000u);                    // 上限 4 秒

            std::cout << "⚠️  GameHeader send failed, retry in " << delay_ms << " ms, left="
                      << handshake_retry_left_-1 << std::endl;

            --handshake_retry_left_;
            auto self_retry = shared_from_this();
            jitter_timer_.expires_from_now(boost::posix_time::milliseconds(delay_ms));
            jitter_timer_.async_wait([self_retry](const boost::system::error_code&){
                if (!self_retry->completed_)
                    self_retry->send_game_header();
            });
            return;
        }
        complete_handshake(false, "GameHeader send failed: " + error.message());
        return;
    }
    
    // === 修改：读取固定 28 字节 GameHeaderRep，避免拆包导致超时 ===
    if (receive_buffer_.size() < sizeof(GameHeaderRep)) {
        receive_buffer_.resize(sizeof(GameHeaderRep));
    }

    boost::asio::async_read(socket_,
        boost::asio::buffer(receive_buffer_.data(), sizeof(GameHeaderRep)),
        [self = shared_from_this()](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_game_header_response(error, bytes_transferred);
        });
}

void MiniBoosterSession::UserSession::handle_game_header_response(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;
    
    if (error) {
        if (handshake_retry_left_ > 0) {
            uint32_t attempt = mini_booster_.config_.handshake_retry - handshake_retry_left_;
            uint32_t delay_ms = 100u * (1u << attempt);
            delay_ms = std::min(delay_ms, 4000u);
            std::cout << "⚠️  GameHeader response read error, retry in " << delay_ms 
                      << " ms, left=" << handshake_retry_left_-1 << std::endl;
            --handshake_retry_left_;
            auto self_retry = shared_from_this();
            jitter_timer_.expires_from_now(boost::posix_time::milliseconds(delay_ms));
            jitter_timer_.async_wait([self_retry](const boost::system::error_code&){
                if (!self_retry->completed_)
                    self_retry->send_game_header();
            });
            return;
        }
        ErrorLogger::instance().log_error("GameHeader响应读取错误: 用户ID=" + std::to_string(user_id_) + 
                                        ", 错误=" + error.message() + 
                                        ", 错误码=" + std::to_string(error.value()));
        complete_handshake(false, "GameHeader response failed: " + error.message());
        return;
    }
    
    // 检查响应
    if (bytes_transferred < sizeof(GameHeaderRep)) {
        if (handshake_retry_left_ > 0) {
            uint32_t attempt = mini_booster_.config_.handshake_retry - handshake_retry_left_;
            uint32_t delay_ms = 100u * (1u << attempt);
            delay_ms = std::min(delay_ms, 4000u);
            std::cout << "⚠️  Incomplete GameHeaderRep, retry in " << delay_ms 
                      << " ms, left=" << handshake_retry_left_-1 << std::endl;
            --handshake_retry_left_;
            auto self_retry = shared_from_this();
            jitter_timer_.expires_from_now(boost::posix_time::milliseconds(delay_ms));
            jitter_timer_.async_wait([self_retry](const boost::system::error_code&){
                if (!self_retry->completed_)
                    self_retry->send_game_header();
            });
            return;
        }
        ErrorLogger::instance().log_error("GameHeader响应不完整: 用户ID=" + std::to_string(user_id_) + 
                                        ", 接收字节数=" + std::to_string(bytes_transferred) + 
                                        ", 期望字节数=" + std::to_string(sizeof(GameHeaderRep)));
        complete_handshake(false, "Incomplete GameHeaderRep response");
        return;
    }
    
    // 解析响应
    GameHeaderRep* rep = reinterpret_cast<GameHeaderRep*>(receive_buffer_.data());
    
    // 记录握手响应详情，使用saved_session_id_替代session_id_
    ErrorLogger::instance().log_info("GameHeader响应: 用户ID=" + std::to_string(user_id_) + 
                                   ", 会话ID=" + std::to_string(saved_session_id_) + 
                                   ", 签名=0x" + std::to_string(rep->nSignture) + 
                                   ", 结果码=" + std::to_string(rep->nResult));
    
    if (rep->nSignture != CC_SOCK_SIGNTURE || rep->nResult != 0) {
        if (handshake_retry_left_ > 0) {
            uint32_t attempt = mini_booster_.config_.handshake_retry - handshake_retry_left_;
            uint32_t delay_ms = 100u * (1u << attempt);
            delay_ms = std::min(delay_ms, 4000u);
            std::cout << "⚠️  Invalid GameHeaderRep, retry in " << delay_ms 
                      << " ms, left=" << handshake_retry_left_-1 << std::endl;
            --handshake_retry_left_;
            auto self_retry = shared_from_this();
            jitter_timer_.expires_from_now(boost::posix_time::milliseconds(delay_ms));
            jitter_timer_.async_wait([self_retry](const boost::system::error_code&){
                if (!self_retry->completed_)
                    self_retry->send_game_header();
            });
            return;
        }
        
        // 记录握手失败原因
        std::string error_reason;
        if (rep->nSignture != CC_SOCK_SIGNTURE) {
            error_reason = "无效签名";
        } else if (rep->nResult == 1) {
            error_reason = "服务器内部错误";
        } else if (rep->nResult == 2) {
            error_reason = "会话ID冲突";
        } else if (rep->nResult == 3) {
            error_reason = "无效的应用ID";
        } else if (rep->nResult == 4) {
            error_reason = "无效的游戏端口";
        } else {
            error_reason = "未知错误";
        }
        
        ErrorLogger::instance().log_error("GameHeader握手失败: 用户ID=" + std::to_string(user_id_) + 
                                        ", 会话ID=" + std::to_string(saved_session_id_) + 
                                        ", 原因=" + error_reason);
        
        complete_handshake(false, "GameHeader response invalid");
        return;
    }
    
    // 第一阶段成功，发送LOGIN消息
    send_login_message();

    // 成功收到响应，取消超时计时器
    timeout_timer_.cancel();
}

void MiniBoosterSession::UserSession::send_login_message() {
    if (completed_) return;
    
    // 构造LOGIN消息 - 第二阶段
    struct Command {
        uint32_t wSignture;   // 'IOCP'
        uint32_t wCommandId;  // MESSAGE_LOGIN=1
        uint32_t wPacketLen;  // sizeof(RuleMesageLogin)=36
        uint32_t wSequenceId; // 序列号
    };
    
    Command command = {};
    command.wSignture = 0x494F4350;    // 'IOCP' 小端整数，与网关 m_nHeaderSignture 完全一致
    command.wCommandId = 1;            // MESSAGE_LOGIN
    command.wPacketLen = sizeof(RuleMesageLogin);
    command.wSequenceId = 0;
    
    // 构造登录消息包
    RuleMesageLogin login = {};
    login.nUserId = 0;                         // 登录阶段 UserId 必须为 0
    strncpy_safe(login.szAppId, sizeof(login.szAppId), mini_booster_.config_.app_id.c_str());
    
    // 使用独立缓冲区
    auto send_buf = std::make_shared<std::vector<uint8_t>>(sizeof(Command) + sizeof(RuleMesageLogin));
    memcpy(send_buf->data(), &command, sizeof(Command));
    memcpy(send_buf->data() + sizeof(Command), &login, sizeof(RuleMesageLogin));
    
    boost::asio::async_write(socket_, boost::asio::buffer(*send_buf),
        [this, send_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            handle_login_message_send(error, bytes_transferred);
        });
}

void MiniBoosterSession::UserSession::handle_login_message_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;
    
    if (error) {
        complete_handshake(false, "Login message send failed: " + error.message());
        return;
    }
    
    // === 分段读取 LOGIN 响应（16 字节头 + JSON 体） ===
    auto self = shared_from_this();

    // Step 1: 读取固定 16 字节 Command 头
    boost::asio::async_read(socket_, boost::asio::buffer(receive_buffer_.data(), 16),
        [self](const boost::system::error_code& ec_header, std::size_t /*bt_header*/) {
            if (ec_header) {
                self->complete_handshake(false, "Login header read failed: " + ec_header.message());
                return;
            }

            struct Command { uint32_t wSignture; uint32_t wCommandId; uint32_t wPacketLen; uint32_t wSequenceId; };
            const Command* cmd = reinterpret_cast<const Command*>(self->receive_buffer_.data());

            if (cmd->wSignture != 0x494F4350 || cmd->wCommandId != 2) { // MESSAGE_LOGIN_REP=2
                self->complete_handshake(false, "Invalid login response header");
                return;
            }

            size_t body_len = cmd->wPacketLen;
            if (body_len == 0) {
                self->complete_handshake(false, "Login response body length is 0");
                return;
            }

            if (self->receive_buffer_.size() < 16 + body_len) {
                self->receive_buffer_.resize(16 + body_len);
            }

            // Step 2: 读取 JSON 体
            boost::asio::async_read(self->socket_, boost::asio::buffer(self->receive_buffer_.data() + 16, body_len),
                [self, body_len](const boost::system::error_code& ec_body, std::size_t /*bt_body*/) {
                    // 无论成功或失败都复用现有 handle_login_response 逻辑
                    self->handle_login_response(ec_body, 16 + body_len);
                });
        });
}

void MiniBoosterSession::UserSession::handle_login_response(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;
    
    if (error) {
        complete_handshake(false, "Login response failed: " + error.message());
        return;
    }
    
    // 检查是否收到完整的响应（至少16字节Command）
    if (bytes_transferred < 16) {
        complete_handshake(false, "Incomplete login response");
        return;
    }
    
    // 解析Command头
    struct Command {
        uint32_t wSignture;
        uint32_t wCommandId;
        uint32_t wPacketLen;
        uint32_t wSequenceId;
    };
    
    const Command* cmd = reinterpret_cast<const Command*>(receive_buffer_.data());
    
    if (cmd->wSignture != 0x494F4350 || cmd->wCommandId != 2) { // MESSAGE_LOGIN_REP=2
        complete_handshake(false, "Invalid login response format");
        return;
    }
    
    // 检查是否有JSON数据
    if (cmd->wPacketLen == 0 || bytes_transferred < 16 + cmd->wPacketLen) {
        complete_handshake(false, "No JSON data in login response");
        return;
    }
    
    // 解析JSON响应
    std::string json_str(reinterpret_cast<const char*>(receive_buffer_.data() + 16), cmd->wPacketLen);
    
    std::cout << "📥 User " << user_id_ << " received login JSON: " << json_str << std::endl;
    
    // 简单的JSON解析（查找UserId）
    // 格式: {"AppID":"8888","UserId":100000002,"Listen":[{"IP":"*********","Port":[8888]}]}
    
    if (!parse_login_response(json_str, login_result_)) {
        complete_handshake(false, "Failed to parse login JSON");
        return;
    }

    std::cout << "✅ User " << user_id_ << " LOGIN SUCCESS: Assigned UserId=" << login_result_.user_id 
              << ", ListenIP=" << login_result_.listen_ip 
              << ", ListenPort=" << login_result_.listen_port << std::endl;

    // === TS1: 控制 LOGIN 成功 ===
    ts_ctrl_ok_ = std::chrono::steady_clock::now();
    if (ts_connect_ok_.time_since_epoch().count() > 0) {
        auto delta_ctrl_ms = std::chrono::duration_cast<std::chrono::milliseconds>(ts_ctrl_ok_ - ts_connect_ok_).count();
        {
            std::ostringstream oss;
            oss << "[LAT] user " << user_id_ << " ctrl_login=" << delta_ctrl_ms << " ms";
            log_latency_line(oss.str());
        }
    }

    // 保存 RuleServer 分配或确认的最终 UserId，供后续 MESSAGE_DISCONNECT 使用
    saved_user_id_ = login_result_.user_id;

    // 登录成功，启动心跳并读取控制消息
    start_heartbeat();
    start_control_read();

    // 建立业务Session
    establish_business_session();

    // 记录登录成功日志
    ErrorLogger::instance().log_debug("[HS] login_success user=" + std::to_string(user_id_) + " assigned=" + std::to_string(login_result_.user_id));
}

void MiniBoosterSession::UserSession::establish_business_session() {
    // 第三阶段：建立业务Session - 连接到Nginx节点（链路2的关键）
    // ✅ 修复：连接到节点而非网关，实现链路2: 测试机→节点→网关→后端
    
    // 为业务链路生成新的SessionId
    business_session_id_ = generate_session_id();
    
    try {
        boost::asio::ip::tcp::resolver resolver(business_socket_.get_executor());
        business_connect_attempt_++;
        std::cout << "[BUS] async_connect attempt #" << business_connect_attempt_ << " user=" << user_id_
                  << " → " << mini_booster_.config_.node_host << ":" << mini_booster_.config_.node_port << std::endl;
        auto endpoints = resolver.resolve(mini_booster_.config_.node_host, 
                                         std::to_string(mini_booster_.config_.node_port));
        
        auto self = shared_from_this();
        boost::asio::async_connect(business_socket_, endpoints,
            [self](const boost::system::error_code& error, const boost::asio::ip::tcp::endpoint&) {
                self->handle_business_connect(error);
            });
    } catch (const std::exception& e) {
        complete_handshake(false, "Business resolve failed: " + std::string(e.what()));
    }
}

void MiniBoosterSession::UserSession::handle_business_connect(const boost::system::error_code& error) {
    if (completed_) return;
    
    if (error) {
        std::cout << "❌ User " << user_id_ << " business connect failed: " << error.message() << std::endl;
        ErrorLogger::instance().log_metric("[BUS_FAIL] stage=connect uid=" + std::to_string(user_id_) +
                                          " ec=" + std::to_string(error.value()));
        mini_booster_.update_stats_on_connection_drop();
        return;
    }
    
    std::cout << "✅ User " << user_id_ << " business connection to " 
             << mini_booster_.config_.node_host << ":" << mini_booster_.config_.node_port 
             << " SUCCESS" << std::endl;
    
    // 发送88字节的 GameHeader（完全照搬原版游戏客户端行为）
    send_business_game_header();
}

void MiniBoosterSession::UserSession::send_business_game_header() {
    if (completed_) return;
    
    // 真实龙盾客户端在业务通道上发送完整 88 字节 GameHeader，
    // 然后等待 8 字节 Echo，再继续 PCOI Cmd1。

    GameHeader header{};
    header.nSignture    = CC_SOCK_SIGNTURE;           // 0x12345678
    header.nUserId      = login_result_.user_id;      // 使用登录阶段分配的真实 UserId
    header.nSessionId   = business_session_id_;       // 使用业务链路专用SessionId
    header.nLocalAddress= ntohl(get_local_ip());             // 用 node_host 作为公网IP
    header.nServerPort  = login_result_.listen_port;  // 业务端口 (如 8888)
    if (header.nUserId == 0) {
        // 如果仍为 0（异常），回退到 start_user_id 基础 + 本地 user_id_ 偏移
        header.nUserId = mini_booster_.config_.start_user_id + (user_id_ % 100000);
    }
    header.nReconnect   = 0;                          // 首次
    header.nListenIp    = ntohl(inet_addr(login_result_.listen_ip.c_str())); // listen_ip JSON 字符串是"*********"，先 inet_addr 得到网络序，再 ntohl 转为主机序
    std::memset(header.Reserved, 0, sizeof(header.Reserved));

    std::memset(header.szAppId, 0, sizeof(header.szAppId));
    strncpy_safe(header.szAppId, sizeof(header.szAppId), mini_booster_.config_.app_id.c_str());

    std::memset(header.szMacAddress, 0, sizeof(header.szMacAddress));
    std::string mac = get_mac_address();
    strncpy_safe(header.szMacAddress, sizeof(header.szMacAddress), mac.c_str());

    // 发送 88 字节 GameHeader
    auto send_buf = std::make_shared<std::vector<uint8_t>>(sizeof(GameHeader));
    std::memcpy(send_buf->data(), &header, sizeof(GameHeader));

    auto self = shared_from_this();
    boost::asio::async_write(business_socket_, boost::asio::buffer(*send_buf),
        [self, send_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_business_inject_send(error, bytes_transferred); // 复用相同回调读取8字节 Echo
        });
}

// 发送 InjectHeader 完成后的处理 – 读取网关回显 8 字节确认包
void MiniBoosterSession::UserSession::handle_business_inject_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;

    if (error) {
        std::cout << "❌ User " << user_id_ << " business InjectHeader send failed: " << error.message() << std::endl;
        mini_booster_.update_stats_on_connection_drop();
        return;
    }

    std::cout << "📤 User " << user_id_ << " sent " << bytes_transferred << " bytes business GameHeader (88B), waiting for 8-byte echo..." << std::endl;

    // 读取 8 字节回显 (网关会返回一个 8 字节确认包)
    auto echo_buf = std::make_shared<std::array<uint8_t, 8>>();
    auto self = shared_from_this();
    boost::asio::async_read(business_socket_, boost::asio::buffer(*echo_buf),
        [self, echo_buf](const boost::system::error_code& ec, std::size_t bt) {
            self->handle_business_inject_echo(ec, bt);
        });
}

void MiniBoosterSession::UserSession::handle_business_inject_echo(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;

    if (error) {
        std::cout << "❌ User " << user_id_ << " read inject echo failed: " << error.message() << std::endl;
        mini_booster_.update_stats_on_connection_drop();
        return;
    }

    std::cout << "✅ User " << user_id_ << " received " << bytes_transferred << " bytes echo, next → PCOI Cmd=1" << std::endl;

    // 继续发送 PCOI 登录命令
    send_business_pcoi_login();
}

void MiniBoosterSession::UserSession::handle_business_header_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;
    
    if (error) {
        std::cout << "❌ User " << user_id_ << " business GameHeader send failed: " << error.message() << std::endl;
        ErrorLogger::instance().log_metric("[BUS_FAIL] stage=header uid=" + std::to_string(user_id_) +
                                          " ec=" + std::to_string(error.value()));
        mini_booster_.update_stats_on_connection_drop();
        return;
    }
    
    std::cout << "📤 User " << user_id_ << " sent " << bytes_transferred << " bytes business GameHeader, next → PCOI Cmd=1" << std::endl;
    
    // 发送 PCOI Cmd1 (Login) —— 52 字节
    send_business_pcoi_login();
}

// 发送 PCOI Cmd=1：仅携带 AppID("8888")/UserId
void MiniBoosterSession::UserSession::send_business_pcoi_login() {
    if (completed_) return;

    PcoiCommandHeader cmd{};
    cmd.wSignture   = PCOI_SIGNATURE;            // 'PCOI'
    cmd.wCommandId  = 1;                         // LOGIN
    cmd.wPacketLen  = sizeof(RuleMesageLogin);   // 36
    cmd.wSequenceId = 0;

    RuleMesageLogin body{};
    body.nUserId = login_result_.user_id;
    memset(body.szAppId, 0, sizeof(body.szAppId));
    strncpy_safe(body.szAppId, sizeof(body.szAppId), mini_booster_.config_.app_id.c_str());

    // 组合发送缓冲区
    auto send_buf = std::make_shared<std::vector<uint8_t>>(sizeof(cmd) + sizeof(body));
    std::memcpy(send_buf->data(), &cmd, sizeof(cmd));
    std::memcpy(send_buf->data() + sizeof(cmd), &body, sizeof(body));

    auto self = shared_from_this();
    boost::asio::async_write(business_socket_, boost::asio::buffer(*send_buf),
        [self, send_buf](const boost::system::error_code& ec, std::size_t bt) {
            self->handle_business_pcoi_login_send(ec, bt);
        });
}

void MiniBoosterSession::UserSession::handle_business_pcoi_login_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (completed_) return;

    if (error) {
        std::cout << "❌ User " << user_id_ << " PCOI Cmd1 send failed: " << error.message() << std::endl;
        ErrorLogger::instance().log_metric("[BUS_FAIL] stage=pcoi uid=" + std::to_string(user_id_) +
                                          " ec=" + std::to_string(error.value()));
        mini_booster_.update_stats_on_connection_drop();
        return;
    }

    std::cout << "📤 User " << user_id_ << " sent " << bytes_transferred << " bytes PCOI Cmd1 (Login)" << std::endl;

    // 至此认为业务 Session 完整建立
    business_session_established_ = true;
    mini_booster_.update_stats_on_business_session_established();

    // 在业务 Session 确认成功后，延迟启动第四数据隧道，防止隧道比网关映射表先到。
    // schedule_data_tunnel_start();  // 隧道功能已关闭

    // ⚡ Hotfix（可选）：某些环境下 200 ms 定时器可能被取消 / 跳过，导致隧道未建立。
    // 若需要保持与原版龙盾客户端严格一致的时序，可在编译时 **不定义** IMMEDIATE_TUNNEL_HOTFIX。
    // 隧道功能已关闭
    /*
#ifdef IMMEDIATE_TUNNEL_HOTFIX
    if (mini_booster_.config_.enable_data_tunnel) {
        std::cout << "[DEBUG] hotfix immediate establish_data_tunnel()" << std::endl;
        establish_data_tunnel();
    }
#endif
    */

    // ② 标记业务握手整体成功，取消 8 s 超时
    complete_handshake(true, "Business session established");

    // ③ 开启业务通道只读和保活定时器
    start_business_read_only();          // 开始接收 JSON & 心跳
    start_business_keepalive();          // 定时发送 Cmd3
    std::cout << "🚀 User " << user_id_ << " business ready, start data TX" << std::endl;
    send_business_data_packet();
}

void MiniBoosterSession::UserSession::start_business_read_only() {
    if (!business_session_established_) return;
    
    std::cout << "👁️  User " << user_id_ << " starting business socket read-only mode..." << std::endl;
    
    // 业务socket进入只读模式，等待服务器数据
    // 这是照搬原版GameClient的行为 - 业务连接主要是接收数据，不主动发送
    {
        auto self = shared_from_this();
        business_socket_.async_read_some(boost::asio::buffer(business_receive_buffer_),
            [self](const boost::system::error_code& error, std::size_t bytes_transferred) {
                self->handle_business_read(error, bytes_transferred);
            });
    }
}

void MiniBoosterSession::UserSession::handle_business_read(const boost::system::error_code& error, std::size_t bytes_transferred) {
    // 当会话正在关闭或连接被正常重置时，直接静默返回，避免无意义的错误日志
    if (session_should_stop_) {
        return;
    }

    if (error) {
        // Windows: 10054 (WSAECONNRESET)；非 Windows: boost::asio::error::connection_reset
        bool is_conn_reset =
#ifdef _WIN32
            (error.value() == WSAECONNRESET);
#else
            (error == boost::asio::error::connection_reset);
#endif

        if (error == boost::asio::error::eof || is_conn_reset) {
            // 🔔 连接被对端优雅关闭或复位，仍然需要做统一的清理逻辑，
            //     否则 socket 句柄会一直占用，累计到服务器阈值后导致批量断开。
            std::cout << "🔌 User " << user_id_ << " business connection closed (" << error.message() << ")" << std::endl;
        } else {
            // 只有真正异常才打印
            std::cout << "📖 User " << user_id_ << " business read error: " << error.message() << std::endl;
        }

        // 统一清理 & 统计
        if (business_socket_.is_open()) {
            boost::system::error_code ec;
            business_socket_.shutdown(boost::asio::ip::tcp::socket::shutdown_both, ec);
            business_socket_.close(ec);
        }

        business_session_established_ = false;
        mini_booster_.update_stats_on_connection_drop();

        // 如配置允许则尝试重连
        if (analyze_error_for_reconnect(error) && mini_booster_.config_.reconnect_config.enabled) {
            start_reconnect_sequence(ReconnectTrigger::NETWORK_ERROR);
        }
        return;
    }
    
    if (bytes_transferred > 0) {
        std::cout << "📖 User " << user_id_ << " received " << bytes_transferred << " bytes from business connection" << std::endl;
        
        // 更新接收统计
        mini_booster_.update_stats_on_bytes_sent(bytes_transferred); // 复用统计函数
    }
    
    // 只有在连接仍然建立时才继续读取
    if (business_session_established_ && business_socket_.is_open()) {
    {
        auto self = shared_from_this();
        business_socket_.async_read_some(boost::asio::buffer(business_receive_buffer_),
            [self](const boost::system::error_code& error, std::size_t bytes_transferred) {
                self->handle_business_read(error, bytes_transferred);
            });
    }
    }
}

void MiniBoosterSession::UserSession::start_business_data_sending() {
    if (completed_ || !business_session_established_) return;
    
    // 🔧 修复：从5秒减少到0.5秒，大幅减少等待时间，更快进入数据传输阶段
    std::cout << "⏳ User " << user_id_ << " waiting 0.5 seconds for gateway to establish upstream connection..." << std::endl;
    
    business_data_timer_.expires_from_now(boost::posix_time::milliseconds(500));
    business_data_timer_.async_wait([self = shared_from_this()](const boost::system::error_code& error) {
        if (!error && !self->session_should_stop_ && self->business_session_established_ && self->business_socket_.is_open()) {
            std::cout << "🚀 User " << self->user_id_ << " starting business data transmission..." << std::endl;
            self->send_business_data_packet();
    }
    });
}

void MiniBoosterSession::UserSession::send_business_data_packet() {
    if (session_should_stop_ || !business_socket_.is_open()) return;

    // === 写入队列限制：避免队列积压 ===
    int current_pending = pending_writes_.load();
    if (current_pending >= MAX_PENDING_WRITES) {
        // 🔧 添加队列满的监控日志和统计
        mini_booster_.update_stats_on_queue_full();
        static thread_local int queue_full_count = 0;
        if (++queue_full_count % 100 == 0) {  // 每100次记录一次
            std::cout << "⚠️ User " << user_id_ << " queue full (pending=" << current_pending << "), skipped " << queue_full_count << " sends" << std::endl;
        }
        return;
    }

    // 1) 优化：使用预生成的随机包大小，减少CPU开销
    static thread_local std::vector<size_t> packet_sizes;
    static thread_local size_t size_index = 0;

    // 首次使用时预生成100个随机大小
    if (packet_sizes.empty()) {
        std::mt19937 gen{std::random_device{}()};
        std::normal_distribution<> nd(100.0, 50.0);
        packet_sizes.reserve(100);

        for (int i = 0; i < 100; ++i) {
            size_t size = 0;
            do {
                size = static_cast<size_t>(std::round(nd(gen)));
            } while (size < 20 || size > 512);
            packet_sizes.push_back(size);
        }
    }

    // 循环使用预生成的大小
    size_t packet_len = packet_sizes[size_index];
    size_index = (size_index + 1) % packet_sizes.size();

    // 2) 准备发送缓冲区（优化：使用简单的递增模式，减少随机数生成开销）
    if (business_data_buffer_.size() < packet_len) {
        business_data_buffer_.resize(packet_len);
    }

    // 使用简单的递增模式填充数据，避免每次都生成随机数
    static thread_local uint8_t fill_byte = 0;
    for (size_t i = 0; i < packet_len; ++i) {
        business_data_buffer_[i] = fill_byte++;
    }

    auto send_buf = std::make_shared<std::vector<uint8_t>>(business_data_buffer_.begin(), business_data_buffer_.begin() + packet_len);

    // 记录一次写尝试，便于判断写投递是否停止
    ErrorLogger::instance().log_metric("[BUS_WRITE_TRY]");

    // 增加待写入计数
    pending_writes_.fetch_add(1);

    auto self = shared_from_this();
    boost::asio::async_write(business_socket_, boost::asio::buffer(*send_buf),
        [self, send_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_business_data_packet_send(error, bytes_transferred);
        });
}

void MiniBoosterSession::UserSession::handle_business_data_packet_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    // 减少待写入计数
    pending_writes_.fetch_sub(1);

    if (error) {
        // ==== 简化日志: 仅输出 WRITE_ERR + 内核配额快照 ====
        DWORD handleCnt = 0;
        GetProcessHandleCount(GetCurrentProcess(), &handleCnt);

        PERFORMANCE_INFORMATION pi{sizeof(pi)};
        SIZE_T nonPagedKB = 0;
        if (GetPerformanceInfo(&pi, sizeof(pi))) {
            // 使用 KernelNonpaged 统计非分页池页数
            nonPagedKB = pi.KernelNonpaged * pi.PageSize / 1024;
        }

        ErrorLogger::instance().log_metric("[WRITE_ERR] code=" + std::to_string(error.value()));
        ErrorLogger::instance().log_metric("[AFD_QUOTA] NPPool=" + std::to_string(nonPagedKB) + "KB Handle=" + std::to_string(handleCnt));
        
        // 错误处理
        mini_booster_.update_stats_on_connection_drop();
        
        // 尝试重连
        if (analyze_error_for_reconnect(error) && mini_booster_.config_.reconnect_config.enabled) {
            start_reconnect_sequence(ReconnectTrigger::NETWORK_ERROR);
        }
        
        // booster.log 已禁用，无需额外记录
        
        return;
    }
    
    // 成功发送业务数据
    ErrorLogger::instance().log_metric("[BUS_WRITE_DONE]");
    mini_booster_.update_stats_on_business_packet_sent();
    // 🔧 使用新的业务数据统计方法
    mini_booster_.update_stats_on_business_data_sent(bytes_transferred);

    // 🔧 添加业务数据发送的控制台日志
    std::cout << "📤 User " << user_id_ << " sent business data (" << bytes_transferred << " bytes)" << std::endl;

    // 🔧 添加业务数据发送的booster.log日志
    ErrorLogger::instance().log("[BUS_DATA] User " + std::to_string(user_id_) + " sent " + std::to_string(bytes_transferred) + " bytes");

    // 追加 metrics 日志，便于离线分析
    ErrorLogger::instance().log_metric("[PAYLOAD_SENT] " + std::to_string(bytes_transferred));
        
    // 计算下一次发送的间隔时间
    uint32_t interval_ms = 0;
    if (mini_booster_.config_.packets_per_second > 0) {
        interval_ms = 1000 / mini_booster_.config_.packets_per_second;
        if (interval_ms == 0) interval_ms = 20; // 最小间隔20ms，防止过快发送
    } else {
        interval_ms = mini_booster_.config_.business_data_interval_ms;
    }

    // 🔧 自适应流量控制：根据队列满情况动态调整发送间隔
    if (mini_booster_.stats_.adaptive_throttling_enabled.load()) {
        double throttle_factor = mini_booster_.stats_.current_throttle_factor.load();
        interval_ms = static_cast<uint32_t>(interval_ms / throttle_factor);

        // 根据队列使用率调整限流因子
        double queue_usage = static_cast<double>(pending_writes_.load()) / MAX_PENDING_WRITES;
        if (queue_usage > 0.8) {
            // 队列使用率超过80%，增加限流
            throttle_factor = std::max(0.1, throttle_factor * 0.9);
        } else if (queue_usage < 0.3) {
            // 队列使用率低于30%，减少限流
            throttle_factor = std::min(1.0, throttle_factor * 1.1);
        }
        mini_booster_.stats_.current_throttle_factor.store(throttle_factor);
    }

    // 调度下一次发送
    if (!session_should_stop_ && business_socket_.is_open() && business_session_established_) {
        business_data_timer_.expires_from_now(boost::posix_time::milliseconds(interval_ms));
        business_data_timer_.async_wait([self = shared_from_this()](const boost::system::error_code& error) {
            if (!error && !self->session_should_stop_ && self->business_socket_.is_open() && self->business_session_established_) {
            self->send_business_data_packet();
        }
    });
    }
}

void MiniBoosterSession::UserSession::start_heartbeat() {
    if (!mini_booster_.config_.enable_heartbeat || !socket_.is_open()) return;
    
    uint32_t hb_interval_ms = mini_booster_.config_.rule_ping_interval_ms;
    if (hb_interval_ms == 0) {
        // 兼容旧字段 heartbeat.interval_seconds（秒）
        hb_interval_ms = mini_booster_.config_.heartbeat_interval_seconds * 1000;
        if (hb_interval_ms == 0) hb_interval_ms = 5000; // 最后兜底 5s (与龙盾客户端保持一致)
    }

    std::cout << "⏰ User " << user_id_ << " scheduling heartbeat in "
              << hb_interval_ms << " ms..." << std::endl;

    heartbeat_timer_.expires_from_now(boost::posix_time::milliseconds(hb_interval_ms));
    heartbeat_timer_.async_wait([self = shared_from_this()](const boost::system::error_code& error) {
        if (!error && !self->session_should_stop_ && self->socket_.is_open()) {
            self->send_heartbeat();
        }
    });

    // 启动心跳监视（只启动一次）
    if (!heartbeat_monitor_started_) {
        heartbeat_monitor_started_ = true;
        start_heartbeat_monitor();
    }
}

void MiniBoosterSession::UserSession::send_heartbeat() {
    // 🔧 步骤7：添加session_should_stop_检查
    if (session_should_stop_ || !socket_.is_open()) return;

    // === 性能监控：心跳发送开始 ===
    PERF_START("heartbeat", user_id_);
    PERF_COUNT("heartbeat_attempts");

    // === 计数与日志：控制链心跳发送 ===
    ++hb_sent_;
    ErrorLogger::instance().log_debug("[HB] uid=" + std::to_string(user_id_) +
                                     " sent=" + std::to_string(hb_sent_) +
                                     " ack=" + std::to_string(hb_ack_));
    ErrorLogger::instance().log_metric("[HB] uid=" + std::to_string(user_id_) +
                                      " sent=" + std::to_string(hb_sent_) +
                                      " ack=" + std::to_string(hb_ack_));
    
    std::cout << "💓 User " << user_id_ << " sending heartbeat..." << std::endl;
    
    // 构造MESSAGE_ACTIVE包
    struct Command {
        uint32_t wSignture;   // 'IOCP'
        uint32_t wCommandId;  // MESSAGE_ACTIVE=3
        uint32_t wPacketLen;  // sizeof(RuleMesageActive)
        uint32_t wSequenceId; // 序列号
    };
    
    Command command = {};
    command.wSignture = 0x494F4350;    // 'IOCP'
    command.wCommandId = 3;            // MESSAGE_ACTIVE
    command.wPacketLen = sizeof(RuleMesageActive);
    command.wSequenceId = 0;
    
    // 构造心跳消息包
    RuleMesageActive active = {};
    // RTT(us) -> (ms/2) 与原生客户端保持一致；若无估算则填 0
    active.nDaley = static_cast<uint32_t>(rtt_estimate_us_ / 2000);
    
    // 使用配置中的 node_host 作为公网 IP（例如 111.170.xxx.xxx）
    std::string public_ip = mini_booster_.config_.node_host;
    if (public_ip.empty() || inet_addr(public_ip.c_str()) == INADDR_NONE) {
        // 极端情况退回 get_local_ip()
        uint32_t ip_val = get_local_ip();
        uint32_t ip_be = htonl(ip_val);
        public_ip = inet_ntoa(*(struct in_addr*)&ip_be);
    }
    active.nLocalAddress = ntohl(inet_addr(public_ip.c_str()));   // 统一网络字节序
    // 调试输出
    std::cout << "🛰️  Heartbeat nLocalAddress set to " << public_ip << std::endl;
    
    // 准备发送缓冲区
    std::vector<uint8_t> hb_buf(sizeof(Command) + sizeof(RuleMesageActive));
    memcpy(hb_buf.data(), &command, sizeof(Command));
    memcpy(hb_buf.data() + sizeof(Command), &active, sizeof(RuleMesageActive));
    
    // 记录发送时间用于RTT计算（仅记录控制连接的RTT）
    last_active_send_ = std::chrono::steady_clock::now();
    
    // --- 1. 发送到控制连接 ---
    auto ctrl_buf = std::make_shared<std::vector<uint8_t>>(hb_buf);  // 拷贝一份以保证buffer生命周期
    auto self = shared_from_this();
    boost::asio::async_write(socket_, boost::asio::buffer(*ctrl_buf),
        [self, ctrl_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_heartbeat_send(error, bytes_transferred);
        });

    // 注意：根据原版GameClient分析，业务连接不发送心跳包
    // 业务连接主要是只读模式，接收服务器数据
}

void MiniBoosterSession::UserSession::handle_heartbeat_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    // === 性能监控：心跳发送完成 ===
    PERF_END("heartbeat", user_id_);

    if (error) {
        consecutive_heartbeat_failures_++;
        PERF_COUNT("heartbeat_failures");

        std::cout << "💔 User " << user_id_ << " heartbeat send ERROR: " << error.message()
                 << " (failure " << consecutive_heartbeat_failures_ << ")" << std::endl;
        
        // P4-A 连续心跳失败触发重连
        if (consecutive_heartbeat_failures_ >= mini_booster_.config_.reconnect_config.heartbeat_timeout_threshold) {
            std::cout << "💔 User " << user_id_ << " heartbeat timeout, triggering reconnect" << std::endl;
            mini_booster_.update_stats_on_heartbeat_timeout();
            start_reconnect_sequence(ReconnectTrigger::HEARTBEAT_TIMEOUT);
        return;
    }
        
        // 继续尝试心跳
        start_heartbeat();
        return;
    }
    
    // 心跳成功，重置失败计数器
    consecutive_heartbeat_failures_ = 0;
    
    std::cout << "💓 User " << user_id_ << " heartbeat sent (" << bytes_transferred
              << " bytes) with ListenIP: " << login_result_.listen_ip
              << ", nLocalAddress: 0x" << std::hex << ntohl(inet_addr(login_result_.listen_ip.c_str()))
              << std::dec << std::endl;

    mini_booster_.update_stats_on_heartbeat_sent();
    // 🔧 使用新的心跳数据统计方法
    mini_booster_.update_stats_on_heartbeat_data_sent(bytes_transferred);
    
    // 继续下一轮心跳
    start_heartbeat();
}

void MiniBoosterSession::UserSession::complete_handshake(bool success, const std::string& response_or_error) {
    if (completed_) return;

    // === 性能监控：握手完成 ===
    PERF_END("handshake", user_id_);
    if (success) {
        PERF_COUNT("handshake_success");
    } else {
        PERF_COUNT("handshake_failures");
    }

    // 计算延迟
    auto end_time = std::chrono::steady_clock::now();
    auto latency_us = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_).count();
    auto latency_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_).count();
    
    // 输出详细错误信息用于调试
    if (!success) {
        std::cout << "User " << user_id_ << " handshake failed: " << response_or_error << std::endl;

        // === 新增 FAIL 延迟日志 ===
        std::string stage;
        if (ts_ctrl_ok_.time_since_epoch().count() > 0) {
            stage = "biz"; // 已完成 ctrl_login，但业务握手失败
        } else if (ts_connect_ok_.time_since_epoch().count() > 0) {
            stage = "ctrl"; // 仅 TCP 连接成功
        } else {
            stage = "connect"; // 还未建立 TCP
        }
        auto fail_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_).count();
        {
            std::ostringstream oss;
            oss << "[FAIL] user " << user_id_ << " stage=" << stage
                << " elapsed=" << fail_ms << "ms reason=" << response_or_error;
            log_latency_line(oss.str());
        }
        
        // 记录握手失败详细日志，使用saved_session_id_替代session_id_
        ErrorLogger::instance().log_error("握手失败: 用户ID=" + std::to_string(user_id_) + 
                                        ", 会话ID=" + std::to_string(saved_session_id_) + 
                                        ", 阶段=" + stage + 
                                        ", 原因=" + response_or_error + 
                                        ", 耗时=" + std::to_string(latency_ms) + "ms");

        // 🔧 步骤1: 只有失败时才设置completed_=true
        completed_ = true;
    } else {
        std::cout << "User " << user_id_ << " handshake successful" << std::endl;
        
        // 记录握手成功详细日志，使用saved_session_id_替代session_id_
        ErrorLogger::instance().log_info("握手成功: 用户ID=" + std::to_string(user_id_) + 
                                       ", 会话ID=" + std::to_string(saved_session_id_) + 
                                       ", 耗时=" + std::to_string(latency_ms) + "ms");
        
        // 🔧 步骤1: 成功时不设置completed_=true，让心跳和隧道能够正常启动
        completed_ = true; // 标记握手已完成，供 AutoStressRunner 统计
    }
    
    // 更新统计 - 修复：确保所有握手尝试都被计数
    if (success) {
        // === TS2 Summary Latency ===
        if (ts_connect_ok_.time_since_epoch().count() > 0 && ts_ctrl_ok_.time_since_epoch().count() > 0) {
            auto ts_finish = std::chrono::steady_clock::now();
            auto conn_ms = std::chrono::duration_cast<std::chrono::milliseconds>(ts_connect_ok_ - start_time_).count();
            auto ctrl_ms = std::chrono::duration_cast<std::chrono::milliseconds>(ts_ctrl_ok_ - ts_connect_ok_).count();
            auto biz_ms  = std::chrono::duration_cast<std::chrono::milliseconds>(ts_finish - ts_ctrl_ok_).count();
            {
                std::ostringstream oss;
                oss << "[LAT] user " << user_id_ << " summary: connect=" << conn_ms
                    << "ms ctrl=" << ctrl_ms << "ms biz=" << biz_ms << "ms";
                log_latency_line(oss.str());
            }
        }

        // P4-A 保存重连信息
        if (saved_user_id_ == 0) {
            saved_user_id_ = user_id_;
            // 保持 SessionId 一致，不再重新生成
            // saved_session_id_ = get_session_id();
        }
        session_state_ = SessionState::CONNECTED;
        
        mini_booster_.update_stats_on_handshake_success();
        // 注意：心跳在登录成功后的handle_login_response中启动，不在这里
        if (!control_connection_established_) {
            control_connection_established_ = true;
            mini_booster_.update_stats_on_control_connection_change(1);
        }
    } else {
        session_state_ = SessionState::FAILED;
        mini_booster_.update_stats_on_handshake_failure();
    }
    
    // 取消超时定时器
    timeout_timer_.cancel();
}

void MiniBoosterSession::UserSession::handle_timeout() {
    if (completed_) return;
    
    // 若已完成 ctrl_login 阶段，改为尝试重连，而非直接失败
    if (ts_ctrl_ok_.time_since_epoch().count() > 0 && mini_booster_.config_.reconnect_config.enabled) {
        std::cout << "⏱️ User " << user_id_ << " biz handshake timeout, start reconnect" << std::endl;
        start_reconnect_sequence(ReconnectTrigger::STRESS_INDUCED);
        return;
    }
    
    std::cout << "⏱️ User " << user_id_ << " handshake timeout after "
              << mini_booster_.config_.handshake_timeout_ms << " ms" << std::endl;
    complete_handshake(false, "Handshake timeout");
}

void MiniBoosterSession::UserSession::close() {
    // === 新增：优雅下线 – 在真正关闭 socket 之前，向网关发送 MESSAGE_DISCONNECT ===
    if (!session_should_stop_) { // 避免重复执行
        session_should_stop_ = true;  // 🔧 步骤7：设置停止标志

        // 仅在控制连接已建立且 socket_ 仍然打开时发送
        if (control_connection_established_ && socket_.is_open()) {
            try {
                // === 修正：Message 框架期望 16B COMMAND 头 + 8B 载荷 ===
                struct CmdHeader {
                    uint32_t wSignture;   // 'IOCP' 小端应为 0x50434F49
                    uint32_t wCommandId;  // 5 = MESSAGE_DISCONNECT
                    uint32_t wPacketLen;  // 8 字节载荷
                    uint32_t wSequenceId; // 0
                } header{};

                header.wSignture  = 0x494F4350;        // 'IOCP'
                header.wCommandId = 5;
                header.wPacketLen = sizeof(uint32_t) * 2; // 8
                header.wSequenceId= 0;

                uint32_t payload[2] = { saved_user_id_, saved_session_id_ };

                std::vector<uint8_t> send_buf(sizeof(CmdHeader) + sizeof(payload));
                std::memcpy(send_buf.data(), &header, sizeof(CmdHeader));
                std::memcpy(send_buf.data() + sizeof(CmdHeader), &payload, sizeof(payload));

                // 启用 TCP_NODELAY，确保立刻发包
                boost::system::error_code ec_nd;
                socket_.set_option(boost::asio::ip::tcp::no_delay(true), ec_nd);
                
                // 调试：即将发送 CMD-5，打印 UserId/SessionId
                std::cout << "[DBG] CMD5 OUT user=" << saved_user_id_
                          << " sess=" << saved_session_id_ << std::endl;

                // --- 发送并记录返回字节数 ---
                boost::system::error_code ec;
                size_t bytes_sent = boost::asio::write(socket_, boost::asio::buffer(send_buf), ec);
                std::cout << "📤 User " << user_id_ << " CMD5 bytes_sent=" << bytes_sent << ", sleeping 1000ms before FIN..." << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                if (ec) {
                    std::cout << "⚠️  User " << user_id_ << " send DISCONNECT failed: " << ec.message() << std::endl;
                } else {
                    std::cout << "📤 User " << user_id_ << " sent DISCONNECT (" << bytes_sent << "B)" << std::endl;
                }

                // === 立即对发送方向做半关闭，并直接强制关闭所有连接（不等待 FIN） ===
                try {
                    boost::system::error_code ec_sd;
                    socket_.shutdown(boost::asio::ip::tcp::socket::shutdown_send, ec_sd);
                } catch (...) {}

                // 立刻终止后台计时器
                business_keepalive_timer_.cancel();
                business_data_timer_.cancel();

                // 直接调用最终关闭，彻底释放所有 socket
                do_final_close();
            } catch (const std::exception& e) {
                std::cout << "⚠️  Exception while sending DISCONNECT: " << e.what() << std::endl;
            }
        }
    }

    // 关闭所有连接（延迟到网关 FIN 或 timeout）
#if 0
    if (socket_.is_open()) {
        std::cout << "[LOG] control socket close()" << std::endl;
        socket_.close();
    }
    if (business_socket_.is_open()) {
        std::cout << "[LOG] business_socket_ close()" << std::endl;
        business_socket_.close();
    }
#endif

    // 取消所有定时器
    timeout_timer_.cancel();
    heartbeat_timer_.cancel();
    heartbeat_monitor_timer_.cancel();  // 确保心跳监视定时器也被取消
    business_data_timer_.cancel();
    business_keepalive_timer_.cancel();
    reconnect_timer_.cancel();  // P4-A 取消重连定时器

    // 🔧 修复：移除隧道连接计数更新，因为不再使用独立数据通道
    // if (data_tunnel_established_) {
    //     mini_booster_.update_stats_on_data_tunnel_change(-1);
    // }

    // 如果启用了数据隧道并且之前业务Session已建立，减少隧道计数（隧道功能已关闭）
    /*
    if (mini_booster_.config_.enable_data_tunnel && business_session_established_) {
        mini_booster_.update_stats_on_data_tunnel_change(-1);
    }
    */

    // 更新业务会话终止统计
    if (business_session_established_) {
        mini_booster_.update_stats_on_business_session_terminated();
        business_session_established_ = false;
    }

    // 在 UserSession::close() 内最后，更新控制连接减少
    if (control_connection_established_) {
        mini_booster_.update_stats_on_control_connection_change(-1);
        control_connection_established_ = false;
    }

    // 关闭数据隧道，取消其内部重连 / 心跳定时器，防止会话结束后仍然自动重连（隧道功能已关闭）
    /*
    if (tunnel_channel_) {
        tunnel_channel_->quiet_close_link();
        tunnel_channel_.reset();
    }
    */

    // === 修改：延长优雅关闭时间到10秒，给网关更多时间处理 ===
    auto self = shared_from_this();
    graceful_close_timer_.expires_from_now(boost::posix_time::seconds(60));
    graceful_close_timer_.async_wait([self](const boost::system::error_code& e){
        if (!e) {
            std::cout << "⏱️  User " << self->user_id_ << " graceful close timer expired, proceeding with final close" << std::endl;
            self->do_final_close();
        }
    });
}

// 真正关闭所有 SOCKET，与 close() 分离，确保有时间等待 CLS
void MiniBoosterSession::UserSession::do_final_close() {
    boost::system::error_code ec;
    
    // 记录关闭前的socket状态
    bool control_was_open = socket_.is_open();
    bool business_was_open = business_socket_.is_open();
    
    if (control_was_open) {
        std::cout << "🔒 User " << user_id_ << " final closing control socket" << std::endl;
        socket_.close(ec);
        if (ec) {
            std::cout << "⚠️  User " << user_id_ << " control socket close error: " << ec.message() << std::endl;
        }
    }
    
    if (business_was_open) {
        std::cout << "🔒 User " << user_id_ << " final closing business socket" << std::endl;
        business_socket_.close(ec);
        if (ec) {
            std::cout << "⚠️  User " << user_id_ << " business socket close error: " << ec.message() << std::endl;
        }
    }
    
    // 隧道功能已关闭
    /*
    if (tunnel_channel_) {
        std::cout << "🔒 User " << user_id_ << " final stopping tunnel channel" << std::endl;
        tunnel_channel_->stop();
    }
    */
    
    std::cout << "✅ User " << user_id_ << " all connections closed (control:" 
              << (control_was_open ? "was_open" : "was_closed") 
              << ", business:" << (business_was_open ? "was_open" : "was_closed") << ")" << std::endl;
}

// 工具方法的简化实现
uint32_t MiniBoosterSession::UserSession::get_session_id() {
    return generate_session_id();
}

uint32_t MiniBoosterSession::UserSession::get_local_ip() {
    // 优先使用配置中的 node_host 作为公网 IP（适用于单节点压测场景）
    try {
        const std::string& host_ip = mini_booster_.config_.node_host;
        uint32_t ip_n = inet_addr(host_ip.c_str());
        if (ip_n != INADDR_NONE) {
            return ip_n; // 已经是网络字节序
        }
    } catch (...) {
        // 忽略
    }

    // 退而求其次：枚举本机网卡，取第一个非回环 IPv4
    try {
        boost::asio::io_context io_ctx;
        boost::asio::ip::tcp::resolver resolver(io_ctx);
        auto endpoints = resolver.resolve(boost::asio::ip::host_name(), "");
        for (const auto& ep : endpoints) {
            auto addr = ep.endpoint().address();
            if (addr.is_v4() && !addr.is_loopback()) {
                return htonl(addr.to_v4().to_uint());
            }
        }
    } catch (const std::exception& e) {
        std::cout << "⚠️  get_local_ip fallback failed: " << e.what() << std::endl;
    }
    
    // 最后回退到 127.0.0.1
    return inet_addr("127.0.0.1");
}

std::string MiniBoosterSession::UserSession::get_mac_address() {
    return "00-1A-2B-3C-4D-5E";  // 固定MAC地址
}

uint32_t MiniBoosterSession::UserSession::generate_session_id() {
    static std::atomic<uint32_t> session_counter{1};
    // 使用毫秒级时间戳，类似于GetTickCount()
    auto now = std::chrono::system_clock::now();
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    uint32_t session_id = static_cast<uint32_t>(ms) + session_counter++;
    
    // 添加日志记录会话ID生成详情，修复未声明的标识符问题
    // 使用this->session_id_而不是session_id_
    ErrorLogger::instance().log_info("SessionID生成: " + std::to_string(session_id) + 
                                   ", 用户ID=" + std::to_string(user_id_) + 
                                   ", 时间戳=" + std::to_string(ms) + 
                                   ", 计数器=" + std::to_string(session_counter.load() - 1));
    
    return session_id;
}

bool MiniBoosterSession::UserSession::parse_login_response(const std::string& json, LoginResult& result) {
    rapidjson::Document d;
    if (d.Parse(json.c_str()).HasParseError()) {
        std::cout << "⚠️  JSON parse error: " << rapidjson::GetParseError_En(d.GetParseError()) << std::endl;
        return false;
    }

    if (!d.IsObject()) return false;

    // UserId
    if (!(d.HasMember("UserId") && d["UserId"].IsUint())) return false;
    result.user_id = d["UserId"].GetUint();

    // Listen / SrcServer 结构
    const rapidjson::Value* listen_array = nullptr;
    if (d.HasMember("Listen") && d["Listen"].IsArray()) {
        listen_array = &d["Listen"];
    } else if (d.HasMember("SrcServer") && d["SrcServer"].IsArray()) {
        listen_array = &d["SrcServer"];
    }
    if (!listen_array || listen_array->Empty()) return false;

    const auto& first = (*listen_array)[0];
    if (!(first.IsObject() && first.HasMember("IP") && first["IP"].IsString())) return false;
    result.listen_ip = first["IP"].GetString();

    // 端口: 随机挑选列表中的一个，避免所有连接集中到同一端口
    if (!(first.HasMember("Port") && first["Port"].IsArray() && !first["Port"].Empty())) return false;
    const auto& portArr = first["Port"];
    // 随机索引（线程安全伪随机即可）
    static thread_local std::mt19937 rng(std::random_device{}());
    std::uniform_int_distribution<size_t> dist(0, portArr.Size() - 1);
    size_t pickIdx = dist(rng);
    if (!portArr[pickIdx].IsUint()) return false;
    result.listen_port = static_cast<uint16_t>(portArr[pickIdx].GetUint());

    return true;
}

void MiniBoosterSession::UserSession::handle_connection_error(const std::string& context, const boost::system::error_code& error) {
    complete_handshake(false, context + ": " + error.message());
}

// ===================== D-2 第四隧道实现（已关闭）=====================

/*
void MiniBoosterSession::UserSession::establish_data_tunnel() {
    // 调试输出：确认函数是否被调用及前置条件状态
    std::cout << "[DEBUG] establish_data_tunnel called, completed=" << completed_
              << ", business_ok=" << business_session_established_
              << ", cfg=" << mini_booster_.config_.enable_data_tunnel 
              << ", session_should_stop=" << session_should_stop_ << std::endl;
              
    // 如果会话已标记为停止，不再创建隧道
    if (session_should_stop_) {
        std::cout << "⏹️ User " << user_id_ << " data tunnel skipped (session_should_stop=true)" << std::endl;
        return;
    }
              
    // 🔧 修复: 不再因为 completed_==true 而提前返回
    if (!business_session_established_) return;
    
    // 若配置已关闭隧道，直接返回
    if (!mini_booster_.config_.enable_data_tunnel) {
        return;
    }

    // 此前若 ListenIP 属于回环地址(127.*) 会跳过隧道建立。
    // 现在无论 ListenIP 是否为回环，都统一通过 Proxy 建立第四数据隧道，
    // 并在 Inject 头中写入业务端口以匹配网关行为。

    if (tunnel_inflight_) {
        return; // 已有隧道握手在进行
    }

    if (tunnel_channel_) {
        return; // 隧道已成功建立或仍存活
    }

    tunnel_inflight_ = true; // 标记正在建立

    auto self = shared_from_this();
    auto& io_ctx = static_cast<boost::asio::io_context&>(socket_.get_executor().context());
    // 使用登录 JSON 中下发的 ListenIP 作为 nLocalAddress，保持与真实龙盾客户端一致
    // 转为主机字节序以符合网关期望（网关直接读取little-endian值）
    uint32_t public_ip_network = inet_addr(mini_booster_.config_.node_host.c_str());

    tunnel_channel_ = std::make_shared<TunnelChannel>(
        io_ctx,
        mini_booster_.config_.node_host,           // 远端 Proxy IP
        mini_booster_.config_.node_port,           // 远端 Proxy 端口 (5188)
        login_result_.listen_port,                 // nServerPort 与业务 GameHeader 一致 (服务器端口)
        public_ip_network,
        htonl(inet_addr(login_result_.listen_ip.c_str())), // listen_ip_network (network byte order)
        mini_booster_.config_.app_id,              // AppID "8888"
        login_result_.user_id,                     // UserId
        business_session_id_,                      // 使用业务链路的SessionId
        [self](bool success, const std::string& err) {
            // 再次检查会话是否应该停止
            if (self->session_should_stop_) {
                std::cout << "⏹️ User " << self->user_id_ << " data tunnel callback ignored (session_should_stop=true)" << std::endl;
                if (self->tunnel_channel_) {
                    self->tunnel_channel_->quiet_close_link();
                }
                return;
            }
            
            // --- 隧道回调 ---
            self->tunnel_inflight_ = false; // 清除进行标记

            if (!success) {
                // 失败: 计划重试（指数退避）
                // 清理旧隧道对象，便于下一次重新创建
                self->tunnel_channel_.reset();

                int backoff = self->tunnel_backoff_ms_;
                self->tunnel_backoff_ms_ = std::min(backoff * 2, 8000);

                if (!self->session_should_stop_) {
                    std::cout << "⏱️ User " << self->user_id_ << " schedule tunnel reconnect in " << backoff << " ms" << std::endl;
                    self->tunnel_data_timer_.expires_from_now(boost::posix_time::milliseconds(backoff));
                    self->tunnel_data_timer_.async_wait([weak=self->weak_from_this()](const boost::system::error_code& e){
                        if (!e) {
                            if (auto s = weak.lock()) {
                                s->establish_data_tunnel();
                            }
                        }
                    });
                }
                return;
            }

            // 成功: 重置退避
            self->tunnel_backoff_ms_ = 100;

            // 更新统计
            self->mini_booster_.update_stats_on_data_tunnel_established();
            self->mini_booster_.update_stats_on_data_tunnel_change(+1);
            std::cout << "🔄 User " << self->user_id_ << " data tunnel established. Start payload TX" << std::endl;

            // 启动灌包（按配置）
            const auto& cfg = self->mini_booster_.config_;
            
            // 强制隧道数据发送频率至少为1.0包/秒，确保隧道活跃可见
            double tunnel_pps = std::max(1.0, cfg.packets_per_second);
            
            // 添加明确的日志，确认实际发送频率
            std::cout << "🛰️ User " << self->user_id_ << " tunnel payload TX: configured PPS=" 
                     << cfg.packets_per_second << ", effective PPS=" << tunnel_pps << std::endl;
            
            self->tunnel_channel_->start_payload_tx(cfg.packet_size,
                                                   tunnel_pps,  // 使用强制的最小PPS值
                                                   cfg.tunnel_packet_size_min,
                                                   cfg.tunnel_packet_size_max,
                                                   cfg.tunnel_random_payload,
                                                   [weak=self->weak_from_this()](size_t bytes){
                if(auto s=weak.lock()) {
                    // 检查会话是否应该停止
                    if (!s->session_should_stop_) {
                        s->mini_booster_.update_stats_on_tunnel_bytes_sent(bytes);
                    }
                }
            });
            // 隧道建立成功时无需再调用 complete_handshake，因为业务已成功标记。
            }
    );
    tunnel_channel_->start();
}
*/

// ===================== 业务连接 KEEP-ALIVE (已禁用) =====================
void MiniBoosterSession::UserSession::start_business_keepalive() {
    // 业务连接保持活跃的心跳：每 5 秒发送一个保活包，防止 NAT / 网关空闲超时
    // 使用 session_should_stop_ 而非 completed_，避免握手成功后被意外停用
    if (session_should_stop_ || !business_session_established_ || !business_socket_.is_open()) {
        return;
    }

    constexpr int KEEPALIVE_INTERVAL_SEC = 5; // 从15秒改为5秒，更接近原版龙盾客户端

    business_keepalive_timer_.expires_from_now(boost::posix_time::seconds(KEEPALIVE_INTERVAL_SEC));
    business_keepalive_timer_.async_wait([self = shared_from_this()](const boost::system::error_code& error) {
        if (!error) {
            self->send_business_keepalive_now();
        }
    });
}

void MiniBoosterSession::UserSession::handle_business_keepalive_send(const boost::system::error_code& error, std::size_t /*bytes_transferred*/) {
    if (error) {
        std::cout << "❌ User " << user_id_ << " business keepalive send failed: " << error.message() << std::endl;
        // 尝试重连
        if (analyze_error_for_reconnect(error) && mini_booster_.config_.reconnect_config.enabled) {
            start_reconnect_sequence(ReconnectTrigger::NETWORK_ERROR);
        }
        return;
    }
    // 调度下一次心跳
    start_business_keepalive();
}

void MiniBoosterSession::UserSession::send_business_keepalive_now() {
    if (session_should_stop_ || !business_session_established_ || !business_socket_.is_open()) {
        return;
    }

    // 使用10字节的有意义保活数据包，避免被网关过滤
    auto buf = std::make_shared<std::vector<uint8_t>>(10);
    
    // 填充保活数据，使其看起来像是有效的业务数据包
    // 前4字节使用标识，剩余字节填充KEEP标记，这与原版客户端的行为类似
    (*buf)[0] = 'K'; // Keepalive
    (*buf)[1] = 'E'; // Envelope
    (*buf)[2] = 'E'; // Echo
    (*buf)[3] = 'P'; // Packet
    
    // 剩余填充ALIVE
    memcpy(buf->data() + 4, "ALIVE", 5);
    buf->at(9) = 0;  // 结尾0字节

    std::cout << "💓 User " << user_id_ << " sending business keepalive (10 bytes)" << std::endl;
    
    auto self = shared_from_this();
    boost::asio::async_write(
        business_socket_,
        boost::asio::buffer(*buf),
        [self, buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_business_keepalive_send(error, bytes_transferred);
        }
    );
}

// ===================== P4-A 重连逻辑实现 =====================

bool MiniBoosterSession::UserSession::analyze_error_for_reconnect(const boost::system::error_code& error) {
    // 如果已经请求会话停止，则不再尝试重连
    if (session_should_stop_) {
        return false;
    }
    
    // 网络错误类型分析
    if (error == boost::asio::error::connection_reset ||
        error == boost::asio::error::broken_pipe ||
        error == boost::asio::error::connection_aborted ||
        error == boost::asio::error::connection_refused ||
        error == boost::asio::error::network_down ||
        error == boost::asio::error::network_unreachable ||
        error == boost::asio::error::host_unreachable) {
        return true; // 网络级错误，适合重连
    }
    
    return false; // 其他错误不重连
}

void MiniBoosterSession::UserSession::start_reconnect_sequence(ReconnectTrigger trigger) {
    if (session_state_ == SessionState::FAILED) {
        return; // 已标记为失败，不再重连
    }
    
    disconnect_time_ = std::chrono::steady_clock::now();
    session_state_ = SessionState::RECONNECTING;
    
    // 保存重连信息
    if (saved_user_id_ == 0) {
        saved_user_id_ = user_id_;
        // 保持 SessionId 一致，不再重新生成
        // saved_session_id_ = get_session_id();
    }
    
    reconnect_attempts_++;
    mini_booster_.update_stats_on_reconnect_attempt();
    
    // 文件日志记录
    ErrorLogger::instance().log("REC::ATT user=" + std::to_string(user_id_) +
        " attempt=" + std::to_string(reconnect_attempts_));
    
    std::cout << "🔄 User " << user_id_ << " starting reconnect attempt " 
             << reconnect_attempts_ << "/" << mini_booster_.config_.reconnect_config.max_attempts << std::endl;
    
    // 关闭现有连接
    close_sockets_only();
    
    // 先取消可能遗留的定时器，避免并行触发
    reconnect_timer_.cancel();
    
    // === 固定5秒重连间隔 (与龙盾客户端保持一致) ===
    int backoff_ms = 5000;  // 固定5秒重连间隔

    std::cout << "⏱️ User " << user_id_ << " schedule reconnect in " << backoff_ms << " ms" << std::endl;

    auto self = shared_from_this();
    reconnect_timer_.expires_from_now(boost::posix_time::milliseconds(backoff_ms));
    reconnect_timer_.async_wait([self](const boost::system::error_code& ec){
        if (!ec) {
            self->execute_reconnect();
        }
    });
}

void MiniBoosterSession::UserSession::execute_reconnect() {
    if (session_state_ != SessionState::RECONNECTING) {
        return; // 状态已改变，取消重连
    }
    
    reconnect_start_time_ = std::chrono::steady_clock::now();
    
    std::cout << "🔄 User " << user_id_ << " executing reconnect..." << std::endl;
    
    // 重新创建socket
    socket_ = boost::asio::ip::tcp::socket(socket_.get_executor());
    
    // 开始重连握手
    start_reconnect_handshake();
}

void MiniBoosterSession::UserSession::start_reconnect_handshake() {
    try {
        boost::asio::ip::tcp::resolver resolver(socket_.get_executor());
        auto endpoints = resolver.resolve(mini_booster_.config_.gateway_host, 
                                         std::to_string(mini_booster_.config_.gateway_port));
        
        auto self = shared_from_this();
        boost::asio::async_connect(socket_, endpoints,
            [self](const boost::system::error_code& error, const boost::asio::ip::tcp::endpoint&) {
                self->handle_reconnect_connect(error);
            });
    } catch (const std::exception& e) {
        handle_reconnect_failure("Reconnect resolve failed: " + std::string(e.what()));
    }
}

void MiniBoosterSession::UserSession::handle_reconnect_connect(const boost::system::error_code& error) {
    if (error) {
        handle_reconnect_failure("Reconnect connection failed: " + error.message());
        return;
    }
    
    std::cout << "🔄 User " << user_id_ << " reconnect connected, sending handshake..." << std::endl;
    
    // 发送重连握手包（nReconnect=1）
    send_reconnect_game_header();
}

void MiniBoosterSession::UserSession::send_reconnect_game_header() {
    // 构造重连 GameHeader（关键：nReconnect=1）
    GameHeader header = {};
    header.nSignture = CC_SOCK_SIGNTURE;       // 0x12345678
    header.nUserId = saved_user_id_;           // 使用保存的UserId
    header.nSessionId = saved_session_id_;     // 使用保存的SessionId
    header.nLocalAddress = ntohl(get_local_ip());
    header.nServerPort = 0;                    // 控制连接必须为0
    header.nReconnect = 1;                     // 重连标志
    header.nListenIp = 0;
    
    // 设置 AppId
    memset(header.szAppId, 0, sizeof(header.szAppId));
    strncpy_safe(header.szAppId, sizeof(header.szAppId), mini_booster_.config_.app_id.c_str());
    
    // 设置 MAC地址
    memset(header.szMacAddress, 0, sizeof(header.szMacAddress));
    std::string mac = get_mac_address();
    strncpy_safe(header.szMacAddress, sizeof(header.szMacAddress), mac.c_str());
    
    // 准备发送缓冲区
    auto send_buf = std::make_shared<std::vector<uint8_t>>(sizeof(GameHeader));
    memcpy(send_buf->data(), &header, sizeof(GameHeader));
    
    // 发送重连握手
    auto self = shared_from_this();
    boost::asio::async_write(socket_, boost::asio::buffer(*send_buf),
        [self, send_buf](const boost::system::error_code& error, std::size_t bytes_transferred) {
            self->handle_reconnect_handshake_send(error, bytes_transferred);
        });
}

void MiniBoosterSession::UserSession::handle_reconnect_handshake_send(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (error) {
        handle_reconnect_failure("Reconnect handshake send failed: " + error.message());
        return;
    }
    
    // 接收重连响应
    socket_.async_read_some(boost::asio::buffer(receive_buffer_),
        [this](const boost::system::error_code& error, std::size_t bytes_transferred) {
            handle_reconnect_handshake_response(error, bytes_transferred);
        });
}

void MiniBoosterSession::UserSession::handle_reconnect_handshake_response(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (error) {
        handle_reconnect_failure("Reconnect handshake response failed: " + error.message());
        return;
    }
    
    // 检查响应
    if (bytes_transferred < sizeof(GameHeaderRep)) {
        handle_reconnect_failure("Incomplete reconnect response");
        return;
    }
    
    // 解析响应
    GameHeaderRep* rep = reinterpret_cast<GameHeaderRep*>(receive_buffer_.data());
    if (rep->nSignture != CC_SOCK_SIGNTURE || rep->nResult != 0) {
        handle_reconnect_failure("Reconnect response invalid");
        return;
    }
    
    // 重连成功
    handle_reconnect_success();
}

void MiniBoosterSession::UserSession::handle_reconnect_success() {
    auto reconnect_end_time = std::chrono::steady_clock::now();
    uint64_t reconnect_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        reconnect_end_time - reconnect_start_time_).count();
    
    session_state_ = SessionState::CONNECTED;
    consecutive_heartbeat_failures_ = 0;
    
    std::cout << "✅ User " << user_id_ << " reconnect success in " 
             << reconnect_time_ms << "ms (attempt " << reconnect_attempts_ << ")" << std::endl;
    
    mini_booster_.update_stats_on_reconnect_success(reconnect_time_ms);
    
    // 恢复控制连接监听和心跳
    start_control_read();
    start_heartbeat();
    
    // 检查是否需要随机断线测试
    maybe_simulate_random_disconnect();

    // 文件日志记录
    ErrorLogger::instance().log("REC::SUC user=" + std::to_string(user_id_) +
        " time_ms=" + std::to_string(reconnect_time_ms) +
        " attempt=" + std::to_string(reconnect_attempts_));
}

void MiniBoosterSession::UserSession::handle_reconnect_failure(const std::string& reason) {
    std::cout << "❌ User " << user_id_ << " reconnect failed: " << reason << std::endl;
    
    mini_booster_.update_stats_on_reconnect_failure();
    
    // 检查是否还能重试
    if (reconnect_attempts_ < mini_booster_.config_.reconnect_config.max_attempts) {
        std::cout << "🔄 User " << user_id_ << " will retry reconnect..." << std::endl;
        start_reconnect_sequence(); // 继续重连
    } else {
        std::cout << "💀 User " << user_id_ << " reached max reconnect attempts, giving up" << std::endl;
        session_state_ = SessionState::FAILED;
        mini_booster_.update_stats_on_connection_drop();
    }

    // 文件日志记录
    ErrorLogger::instance().log("REC::FAIL user=" + std::to_string(user_id_) +
        " attempt=" + std::to_string(reconnect_attempts_) +
        " reason=" + reason);
}

void MiniBoosterSession::UserSession::maybe_simulate_random_disconnect() {
    // P4-A 随机断线模拟（可选）
    if (mini_booster_.config_.reconnect_config.failure_probability > 0.0) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_real_distribution<> dis(0.0, 1.0);
        
        if (dis(gen) < mini_booster_.config_.reconnect_config.failure_probability) {
            std::cout << "🎲 User " << user_id_ << " simulating random disconnect" << std::endl;
            close_sockets_only();
            start_reconnect_sequence(ReconnectTrigger::RANDOM_SIMULATION);
        }
    }
}

void MiniBoosterSession::UserSession::close_sockets_only() {
    // 只关闭socket，不更新统计（用于重连场景）
    if (socket_.is_open()) {
        socket_.close();
    }
    if (business_socket_.is_open()) {
        business_socket_.close();
    }
    
    // 取消定时器（除了重连定时器）
    timeout_timer_.cancel();
    heartbeat_timer_.cancel();
    business_data_timer_.cancel();
    business_keepalive_timer_.cancel();
}

// ===================== P4-A 重连统计更新方法 =====================

void MiniBoosterSession::update_stats_on_reconnect_attempt() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.reconnect_attempts++;
}

void MiniBoosterSession::update_stats_on_reconnect_success(uint64_t reconnect_time_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.reconnect_successes++;
    stats_.total_reconnect_time_ms += reconnect_time_ms;
    
    if (reconnect_time_ms < stats_.min_reconnect_time_ms) {
        stats_.min_reconnect_time_ms = reconnect_time_ms;
    }
    if (reconnect_time_ms > stats_.max_reconnect_time_ms) {
        stats_.max_reconnect_time_ms = reconnect_time_ms;
    }
}

void MiniBoosterSession::update_stats_on_reconnect_failure() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.reconnect_failures++;
}

void MiniBoosterSession::update_stats_on_heartbeat_timeout() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.heartbeat_timeouts++;
}

void MiniBoosterSession::update_stats_on_network_error() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.network_errors++;
}

void MiniBoosterSession::update_stats_on_business_session_terminated() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (stats_.active_business_connections > 0) {
        --stats_.active_business_connections;
    }
}

// ===================== MiniBoosterSession 基本方法实现 =====================

MiniBoosterSession::MiniBoosterSession(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , running_(false)
    , current_user_id_(1)
    , handshake_timer_(io_context)
    , global_heartbeat_timer_(io_context)
    , global_business_timer_(io_context) {
    stats_.reset();
}

MiniBoosterSession::~MiniBoosterSession() {
    stop();
}

void MiniBoosterSession::configure(const MiniBoosterConfig& config) {
    config_ = config;
}

bool MiniBoosterSession::start() {
    if (running_) {
        return false;
    }
    
    running_ = true;
    connections_.clear();

    // 🔧 启动全局批处理定时器
    if (batch_processing_enabled_.load()) {
        start_global_timers();
    }

    if (config_.users_count > 0) {
        for (uint32_t i = 0; i < config_.users_count; ++i) {
            uint32_t user_id = config_.start_user_id + (config_.role_id * 100000) + i;
            auto session = std::make_shared<UserSession>(io_context_, *this, user_id);
            session->start_handshake();
            std::lock_guard<std::mutex> lock(connections_mutex_);
            connections_.push_back(session);
        }
        std::cout << "✅ 已启动 " << config_.users_count << " 个用户会话" << std::endl;
    } else {
        std::cout << "✅ MiniBoosterSession 启动 (0 初始用户)" << std::endl;
    }
    return true;
}

void MiniBoosterSession::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;

    // 🔧 停止全局批处理定时器
    stop_global_timers();

    // 停止所有用户会话
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        for (auto& session : connections_) {
            if (session) {
                session->close();
            }
        }
        // 不立即清空 connections_，避免异步回调访问已销毁的 UserSession 导致崩溃。
        // 统一在 MiniBoosterSession 析构或 IO 线程结束后由 vector 自动析构。
    }
    
    std::cout << "✅ 已停止所有用户会话" << std::endl;
}

bool MiniBoosterSession::is_running() const {
    return running_;
}

bool MiniBoosterSession::scale_up(int count) {
    if (!running_) {
        return false;
    }
    
    uint32_t current_users;
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        current_users = static_cast<uint32_t>(connections_.size());
    }
    
    // 添加新的用户会话
    for (int i = 0; i < count; ++i) {
        uint32_t user_id = config_.start_user_id + current_users + i;
        auto session = std::make_shared<UserSession>(io_context_, *this, user_id);
        session->start_handshake();
        
        std::lock_guard<std::mutex> lock(connections_mutex_);
        connections_.push_back(session);
    }
    
    std::lock_guard<std::mutex> lock(connections_mutex_);
    std::cout << "📈 增加了 " << count << " 个用户会话，当前总数: " << connections_.size() << std::endl;
    return true;
}

bool MiniBoosterSession::scale_down(int count) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    
    if (!running_ || connections_.empty()) {
        return false;
    }
    
    int to_remove = std::min(count, static_cast<int>(connections_.size()));
    
    // 移除最后几个用户会话
    for (int i = 0; i < to_remove; ++i) {
        if (!connections_.empty()) {
            connections_.back()->close();
            connections_.pop_back();
        }
    }
    
    std::cout << "📉 减少了 " << to_remove << " 个用户会话，当前总数: " << connections_.size() << std::endl;
    return true;
}

MiniBoosterStats MiniBoosterSession::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void MiniBoosterSession::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.reset();
}

void MiniBoosterSession::print_status() const {
    auto stats = get_stats();
    
    std::cout << "\n=== Mini-Booster 状态 ===" << std::endl;
    std::cout << "运行状态: " << (running_ ? "运行中" : "已停止") << std::endl;
    
    size_t session_count;
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        session_count = connections_.size();
    }
    std::cout << "用户会话数: " << session_count << std::endl;
    std::cout << "握手统计:" << std::endl;
    std::cout << "  总握手数: " << stats.total_handshakes_sent << std::endl;
    std::cout << "  成功握手: " << stats.successful_handshakes << std::endl;
    std::cout << "  失败握手: " << stats.failed_handshakes << std::endl;
    std::cout << "连接统计:" << std::endl;
    std::cout << "  活跃控制连接: " << stats.active_control_connections << std::endl;
    std::cout << "  活跃业务连接: " << stats.active_business_connections << std::endl;
    std::cout << "数据统计:" << std::endl;
    std::cout << "  发送字节数: " << stats.total_bytes_sent << std::endl;
    std::cout << "  接收字节数: " << stats.total_bytes_received << std::endl;
    std::cout << "  业务数据包: " << stats.business_packets_sent << std::endl;
    std::cout << "  心跳包数: " << stats.heartbeat_sent << std::endl;

    // 🔧 添加实时流量统计
    print_traffic_stats();
    std::cout << "重连统计:" << std::endl;
    std::cout << "  重连尝试: " << stats.reconnect_attempts << std::endl;
    std::cout << "  重连成功: " << stats.reconnect_successes << std::endl;
    std::cout << "  重连失败: " << stats.reconnect_failures << std::endl;
    std::cout << "========================\n" << std::endl;
}

void MiniBoosterSession::set_handshake_callback(HandshakeCallback callback) {
    callback_ = callback;
}

// 统计更新方法实现
void MiniBoosterSession::update_stats_on_handshake_success() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.successful_handshakes++;
    stats_.total_handshakes_sent++;
}

void MiniBoosterSession::update_stats_on_handshake_failure() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.failed_handshakes++;
    stats_.total_handshakes_sent++;
}

void MiniBoosterSession::update_stats_on_heartbeat_sent() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.heartbeat_sent++;
}

void MiniBoosterSession::update_stats_on_heartbeat_response() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.heartbeat_responses++;
}

void MiniBoosterSession::update_stats_on_business_packet_sent() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.business_packets_sent++;
}

void MiniBoosterSession::update_stats_on_connection_drop() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.connection_drops++;
}

void MiniBoosterSession::update_stats_on_business_session_established() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.business_sessions_established++;
    stats_.active_business_connections++;
}

void MiniBoosterSession::update_stats_on_bytes_sent(size_t bytes) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.total_bytes_sent += bytes;
}

// 🔧 新增：实时流量统计方法实现
void MiniBoosterSession::update_stats_on_business_data_sent(size_t bytes) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.total_bytes_sent += bytes;
    stats_.business_packets_sent++;
    stats_.business_bytes_sent_last_second += bytes;
    stats_.business_packets_sent_last_second++;
}

void MiniBoosterSession::update_stats_on_heartbeat_data_sent(size_t bytes) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.total_bytes_sent += bytes;
    stats_.heartbeat_sent++;
    stats_.heartbeat_bytes_sent_last_second += bytes;
    stats_.heartbeat_packets_sent_last_second++;
}

void MiniBoosterSession::print_traffic_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.last_stats_time).count();

    if (duration > 0) {
        double business_bps = static_cast<double>(stats_.business_bytes_sent_last_second) / duration;
        double business_pps = static_cast<double>(stats_.business_packets_sent_last_second) / duration;
        double heartbeat_bps = static_cast<double>(stats_.heartbeat_bytes_sent_last_second) / duration;
        double heartbeat_pps = static_cast<double>(stats_.heartbeat_packets_sent_last_second) / duration;

        std::cout << "📊 流量统计 [连接数:" << stats_.active_control_connections << "]:" << std::endl;
        std::cout << "   业务数据: " << business_bps << " B/s, " << business_pps << " pps" << std::endl;
        std::cout << "   心跳数据: " << heartbeat_bps << " B/s, " << heartbeat_pps << " pps" << std::endl;
        std::cout << "   总流量: " << (business_bps + heartbeat_bps) << " B/s" << std::endl;
        std::cout << "   累计发送: " << stats_.total_bytes_sent << " bytes" << std::endl;

        // 🔧 添加客户端资源监控
        std::cout << "💻 客户端资源:" << std::endl;
        std::cout << "   活跃连接: " << stats_.active_control_connections << " 控制 + "
                  << stats_.active_business_connections << " 业务" << std::endl;
        if (stats_.queue_full_events > 0) {
            std::cout << "   ⚠️ 队列满事件: " << stats_.queue_full_events << " 次" << std::endl;
        }
        if (stats_.adaptive_throttling_enabled.load()) {
            double throttle = stats_.current_throttle_factor.load();
            std::cout << "   🎛️ 自适应限流: " << std::fixed << std::setprecision(2)
                      << (throttle * 100) << "%" << std::endl;
        }
    }
}

void MiniBoosterSession::reset_per_second_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.business_bytes_sent_last_second = 0;
    stats_.business_packets_sent_last_second = 0;
    stats_.heartbeat_bytes_sent_last_second = 0;
    stats_.heartbeat_packets_sent_last_second = 0;
    stats_.last_stats_time = std::chrono::steady_clock::now();
}

void MiniBoosterSession::update_stats_on_queue_full() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.queue_full_events++;
}

void MiniBoosterSession::record_traffic_snapshot(AutoStressResult& result) {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.last_stats_time).count();

    // 🔧 修复：即使duration为0也要记录快照，使用默认时间间隔
    if (duration <= 0) {
        duration = 1; // 使用1秒作为默认间隔
    }

    {
        AutoStressResult::TrafficSnapshot snapshot;
        snapshot.connection_count = stats_.active_control_connections;
        snapshot.business_bytes_per_second = static_cast<double>(stats_.business_bytes_sent_last_second) / duration;
        snapshot.business_packets_per_second = static_cast<double>(stats_.business_packets_sent_last_second) / duration;
        snapshot.heartbeat_bytes_per_second = static_cast<double>(stats_.heartbeat_bytes_sent_last_second) / duration;
        snapshot.heartbeat_packets_per_second = static_cast<double>(stats_.heartbeat_packets_sent_last_second) / duration;
        snapshot.total_bytes_per_second = snapshot.business_bytes_per_second + snapshot.heartbeat_bytes_per_second;
        snapshot.cumulative_bytes_sent = stats_.total_bytes_sent;
        snapshot.timestamp = now;

        result.traffic_history.push_back(snapshot);

        // 🔧 记录后立即重置统计计数器，避免定时器不同步问题
        stats_.business_bytes_sent_last_second = 0;
        stats_.business_packets_sent_last_second = 0;
        stats_.heartbeat_bytes_sent_last_second = 0;
        stats_.heartbeat_packets_sent_last_second = 0;
        stats_.last_stats_time = now;

        // 🔧 简化的控制台输出（减少终端噪音）
        std::cout << "📊 [" << snapshot.connection_count << " 连接] 总流量: "
                  << static_cast<int>(snapshot.total_bytes_per_second) << " B/s";

    // 🔧 添加连接成功率监控
    if (stats_.total_handshakes_sent > 0) {
        double success_rate = (double)stats_.successful_handshakes / stats_.total_handshakes_sent * 100.0;
        std::cout << " | 连接成功率: " << std::fixed << std::setprecision(1) << success_rate << "%";
        if (stats_.failed_handshakes > 0) {
            std::cout << " (失败:" << stats_.failed_handshakes << ")";
        }
    }
    std::cout << std::endl;
    }
}

// 隧道统计方法已关闭
/*
void MiniBoosterSession::update_stats_on_data_tunnel_established() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.data_tunnels_established++;
    stats_.active_data_tunnels++;
}

void MiniBoosterSession::update_stats_on_tunnel_packet_sent() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.business_packets_sent_tunnel++;
}

void MiniBoosterSession::update_stats_on_tunnel_bytes_sent(size_t bytes) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.bytes_sent_tunnel += bytes;
}

void MiniBoosterSession::update_stats_on_tunnel_heartbeat_sent() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.tunnel_heartbeat_sent++;
}

void MiniBoosterSession::update_stats_on_data_tunnel_change(int delta) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (delta > 0) {
        stats_.active_data_tunnels += delta;
    } else {
        stats_.active_data_tunnels = (stats_.active_data_tunnels > static_cast<uint64_t>(-delta))
            ? stats_.active_data_tunnels + delta : 0;
    }
}
*/

void MiniBoosterSession::update_stats_on_control_connection_change(int delta) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (delta > 0) {
        stats_.active_control_connections += delta;
    } else {
        uint64_t abs_d = static_cast<uint64_t>(-delta);
        if (stats_.active_control_connections >= abs_d) {
            stats_.active_control_connections -= abs_d;
        } else {
            stats_.active_control_connections = 0;
        }
    }
}

uint32_t MiniBoosterSession::get_next_user_id() {
    static uint32_t next_user_id = config_.start_user_id;
    return next_user_id++;
}

void MiniBoosterSession::schedule_next_handshake() {
    // 简化实现：不需要调度，所有会话同时启动
}

void MiniBoosterSession::create_user_session() {
    // 简化实现：在start()方法中统一创建
}

void MiniBoosterSession::update_stats_on_completion(bool success, uint64_t latency_us, size_t bytes_sent, size_t bytes_received) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (success) {
        stats_.successful_handshakes++;
    } else {
        stats_.failed_handshakes++;
    }
    
    stats_.total_bytes_sent += bytes_sent;
    stats_.total_bytes_received += bytes_received;
    
        // 更新延迟统计
        if (latency_us < stats_.min_latency_us) {
            stats_.min_latency_us = latency_us;
        }
        if (latency_us > stats_.max_latency_us) {
            stats_.max_latency_us = latency_us;
        }
        stats_.total_latency_us += latency_us;
}

void MiniBoosterSession::UserSession::start_control_read() {
    if (!socket_.is_open()) return;
    
    // 对固定长度数组进行清零，保证每次读取前缓冲区干净
    cmd_header_buf_.fill(0);
    
    // 首先读取固定16字节的命令头
    {
        auto self = shared_from_this();
        boost::asio::async_read(socket_, boost::asio::buffer(cmd_header_buf_),
            [self](const boost::system::error_code& error, std::size_t bytes_transferred) {
                self->handle_control_header_read(error, bytes_transferred);
            });
    }
}

void MiniBoosterSession::UserSession::handle_control_header_read(const boost::system::error_code& error, std::size_t bytes_transferred) {
    if (error) {
        handle_control_error(error);
        return;
    }
    
    // 确保收到了完整的 16 字节 Command 头
    if (bytes_transferred != sizeof(uint32_t) * 4) {
        handle_control_error(boost::asio::error::operation_aborted);
        return;
    }

    struct Command {
        uint32_t wSignture;   // 'IOCP' = 0x494F4350
        uint32_t wCommandId;  // 1-6 等指令
        uint32_t wPacketLen;  // 负载长度(字节)
        uint32_t wSequenceId; // 序列号
    } cmd{};
    
    std::memcpy(&cmd, cmd_header_buf_.data(), sizeof(Command));
    
    if (cmd.wSignture != 0x494F4350) {
        handle_control_error(boost::asio::error::operation_aborted);
        return;
    }
    
    // 如果无负载，则直接处理并继续下一轮读取
    if (cmd.wPacketLen == 0) {
        process_control_message(cmd.wCommandId, nullptr, 0);
        start_control_read();
        return;
    }

    // 读取 body
    recv_body_buf_.resize(cmd.wPacketLen);
            auto self = shared_from_this();
            boost::asio::async_read(socket_, boost::asio::buffer(recv_body_buf_),
        [self, cmd](const boost::system::error_code& ec, std::size_t bt) {
            self->handle_control_body_read(ec, bt, cmd.wCommandId, cmd.wSequenceId);
                });
}

void MiniBoosterSession::UserSession::handle_control_body_read(const boost::system::error_code& error, std::size_t bytes_transferred, uint32_t command_id, uint32_t sequence_id) {
    if (error) {
        handle_control_error(error);
        return;
    }
    
    // 处理完整的消息
    process_control_message(command_id, recv_body_buf_.data(), recv_body_buf_.size());
    
    // 继续监听下一个消息
    start_control_read();
}

void MiniBoosterSession::UserSession::process_control_message(uint32_t command_id, const uint8_t* body_data, size_t body_size) {
    switch (command_id) {
        case 4: { // MESSAGE_ACTIVE_REP
            last_active_recv_ = std::chrono::steady_clock::now();
            auto rtt = std::chrono::duration_cast<std::chrono::microseconds>(
                last_active_recv_ - last_active_send_).count();
            rtt_estimate_us_ = static_cast<uint32_t>(rtt);
            
            // 更新心跳响应统计
            mini_booster_.update_stats_on_heartbeat_response();
            // 记录收到心跳响应的时间
            last_heartbeat_ts_ = std::chrono::steady_clock::now();
            
            // 添加反向链路调试信息，确认网关响应心跳状态
            std::cout << "💓 User " << user_id_ << " heartbeat response received, RTT: " 
                     << rtt_estimate_us_ << "μs, 反向链路状态正常" << std::endl;

            // === 计数与日志：收到心跳应答 ===
            ++hb_ack_;
            ErrorLogger::instance().log_debug("[HB] uid=" + std::to_string(user_id_) +
                                             " sent=" + std::to_string(hb_sent_) +
                                             " ack=" + std::to_string(hb_ack_));
            ErrorLogger::instance().log_metric("[HB] uid=" + std::to_string(user_id_) +
                                              " sent=" + std::to_string(hb_sent_) +
                                              " ack=" + std::to_string(hb_ack_));
            // 记录一次心跳发送到 metrics
            ErrorLogger::instance().log_metric("[HB_SENT]");
            break;
        }
        default:
            std::cout << "📨 User " << user_id_ << " received unknown command: " 
                     << command_id << std::endl;
            break;
    }
}

void MiniBoosterSession::UserSession::handle_control_error(const boost::system::error_code& error) {
    // 如果当前正在主动关闭会话，或遇到 EOF / 已取消 等正常结束状态，直接忽略
    if (session_should_stop_ || error == boost::asio::error::eof || error == boost::asio::error::operation_aborted) {
        return;  // 这些都是可预期的正常关闭情形
    }

    // Windows 平台上，连接主动关闭时常见的 WSAECONNRESET (10054) 错误属于预期行为，
    // 非 Windows 使用 boost::asio::error::connection_reset 常量判断。
#ifdef _WIN32
    if (error.value() == WSAECONNRESET) {
        return;  // 忽略连接被重置错误，避免误报
    }
#else
    if (error == boost::asio::error::connection_reset) {
        return;  // 忽略连接被重置错误
    }
#endif

    // P4-A 智能错误分析和重连
    bool should_reconnect = analyze_error_for_reconnect(error);
    
    if (should_reconnect && mini_booster_.config_.reconnect_config.enabled && 
        reconnect_attempts_ < mini_booster_.config_.reconnect_config.max_attempts &&
        session_state_ != SessionState::FAILED) {
        
        std::cout << "🔄 User " << user_id_ << " triggering reconnect due to: " 
                 << error.message() << std::endl;
        
        mini_booster_.update_stats_on_network_error();
        start_reconnect_sequence(ReconnectTrigger::NETWORK_ERROR);
    } else {
        std::cout << "❌ User " << user_id_ << " permanent failure: " 
                 << error.message() << std::endl;
        session_state_ = SessionState::FAILED;
        mini_booster_.update_stats_on_connection_drop();
    }
}

// 在业务 Session 确认成功后，延迟启动第四数据隧道，防止隧道比网关映射表先到。（已关闭）
/*
void MiniBoosterSession::UserSession::schedule_data_tunnel_start() {
    // 🔧 修复: 仅依赖 business_session_established_ 与配置开关
    if (!business_session_established_ || !mini_booster_.config_.enable_data_tunnel) {
        std::cout << "⏯️ User " << user_id_ << " tunnel skipped: business_established=" 
                  << business_session_established_ << ", enable_tunnel=" 
                  << mini_booster_.config_.enable_data_tunnel << std::endl;
        return;
    }

    std::cout << "⏳ User " << user_id_ << " scheduling tunnel start in 200ms..." << std::endl;

    constexpr int kTunnelDelayMs = 200; // 实测 100-200 ms 已足够

    auto self = shared_from_this();
    tunnel_data_timer_.expires_from_now(boost::posix_time::milliseconds(kTunnelDelayMs));
    tunnel_data_timer_.async_wait([self](const boost::system::error_code& error){
        if (error) {
            std::cout << "❌ User " << self->user_id_ << " tunnel timer error: " << error.message() << std::endl;
            return;
        }
        
        if (self->business_session_established_) {
            std::cout << "🔄 User " << self->user_id_ << " tunnel timer fired, establishing tunnel..." << std::endl;
            self->establish_data_tunnel();
        } else {
            std::cout << "⚠️ User " << self->user_id_ << " business session no longer established, cancel tunnel" << std::endl;
        }
    });
}
*/

// === 新增：每 1 s 监视心跳超时 ===
void MiniBoosterSession::UserSession::start_heartbeat_monitor() {
    heartbeat_monitor_timer_.expires_from_now(boost::posix_time::seconds(1));
    auto self = shared_from_this();
    heartbeat_monitor_timer_.async_wait([self](const boost::system::error_code& error) {
        if (error || self->session_should_stop_) return;

        auto now = std::chrono::steady_clock::now();
        if (now - self->last_heartbeat_ts_ > std::chrono::seconds(60)) {
            std::cout << "💔 User " << self->user_id_ << " heartbeat timeout 60s (与龙盾客户端保持一致), forcing disconnect" << std::endl;
            self->mini_booster_.update_stats_on_heartbeat_timeout();
            self->start_reconnect_sequence(ReconnectTrigger::HEARTBEAT_TIMEOUT);
            return;
        }
        // 继续监视
        self->start_heartbeat_monitor();
    });
}

void MiniBoosterSession::UserSession::handle_handshake_timeout() {
    if (completed_) return;

    // 记录握手超时阶段日志，供后续统计脚本使用
    ErrorLogger::instance().log_debug("[HS] handshake_timeout user=" + std::to_string(user_id_));

    if (handshake_retry_left_ > 0) {
        std::cout << "⏱️  User " << user_id_ << " handshake timeout, retry left=" << handshake_retry_left_-1 << std::endl;
        --handshake_retry_left_;
        send_game_header();
    } else {
        complete_handshake(false, "Handshake timeout");
    }
}

// 在适当位置添加更新握手超时统计的方法
void MiniBoosterSession::update_stats_on_handshake_timeout() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.timeout_handshakes++;
}

// === 新增：统计活跃会话数量 ===
bool MiniBoosterSession::UserSession::is_active(std::chrono::steady_clock::time_point now) const {
    // 动态活动窗口：取心跳间隔 * 3，最小 60s，最大 180s
    uint32_t hb_sec = mini_booster_.config_.heartbeat_interval_seconds;
    if (hb_sec == 0) hb_sec = 5; // 默认
    uint32_t window_sec = std::clamp<uint32_t>(hb_sec * 3, 60, 180);

    return completed_ && business_session_established_ && socket_.is_open() && !session_should_stop_ &&
           (now - last_heartbeat_ts_ <= std::chrono::seconds(window_sec));
}

size_t MiniBoosterSession::count_active_sessions() const {
    std::lock_guard<std::mutex> lg(connections_mutex_);
    auto now = std::chrono::steady_clock::now();
    size_t active = 0;
    for (const auto &sess : connections_) {
        if (sess && sess->is_active(now)) ++active;
    }
    return active;
}

// ===================== 🔧 性能优化：批处理定时器实现 =====================

void MiniBoosterSession::start_global_timers() {
    if (!batch_processing_enabled_.load()) return;

    std::cout << "🚀 启动全局批处理定时器..." << std::endl;
    schedule_heartbeat_batch();
    schedule_business_data_batch();

    // 🔧 启动流量统计定时器（每10秒输出一次）
    schedule_traffic_stats_timer();
}

void MiniBoosterSession::schedule_traffic_stats_timer() {
    if (!running_) return;

    // 每10秒输出一次流量统计并重置计数器
    global_heartbeat_timer_.expires_from_now(boost::posix_time::seconds(10));
    global_heartbeat_timer_.async_wait([this](const boost::system::error_code& error) {
        if (!error && running_) {
            print_traffic_stats();
            reset_per_second_stats();
            schedule_traffic_stats_timer(); // 重新调度
        }
    });
}

void MiniBoosterSession::stop_global_timers() {
    global_heartbeat_timer_.cancel();
    global_business_timer_.cancel();
    std::cout << "🛑 全局批处理定时器已停止" << std::endl;
}

void MiniBoosterSession::schedule_heartbeat_batch() {
    if (!running_ || !batch_processing_enabled_.load()) return;

    // 心跳间隔：默认2秒
    uint32_t interval_ms = config_.heartbeat_interval_seconds * 1000;
    if (interval_ms == 0) interval_ms = 2000;

    global_heartbeat_timer_.expires_from_now(boost::posix_time::milliseconds(interval_ms));
    global_heartbeat_timer_.async_wait([this](const boost::system::error_code& error) {
        if (!error && running_) {
            process_heartbeat_batch();
            schedule_heartbeat_batch(); // 重新调度
        }
    });
}

void MiniBoosterSession::schedule_business_data_batch() {
    if (!running_ || !batch_processing_enabled_.load()) return;

    // 业务数据间隔：根据PPS计算
    uint32_t interval_ms = 200; // 默认200ms (5 PPS)
    if (config_.packets_per_second > 0) {
        interval_ms = 1000 / config_.packets_per_second;
        if (interval_ms == 0) interval_ms = 20; // 最小20ms
    }

    global_business_timer_.expires_from_now(boost::posix_time::milliseconds(interval_ms));
    global_business_timer_.async_wait([this](const boost::system::error_code& error) {
        if (!error && running_) {
            process_business_data_batch();
            schedule_business_data_batch(); // 重新调度
        }
    });
}

void MiniBoosterSession::process_heartbeat_batch() {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    size_t total_sessions = connections_.size();
    if (total_sessions == 0) return;

    // 批处理：每次处理一部分用户，避免单次处理过多
    const size_t BATCH_SIZE = 50; // 每批处理50个用户
    size_t start_index = heartbeat_batch_index_.load() % total_sessions;
    size_t processed = 0;

    for (size_t i = 0; i < total_sessions && processed < BATCH_SIZE; ++i) {
        size_t index = (start_index + i) % total_sessions;
        auto& session = connections_[index];

        if (session && session->is_active(std::chrono::steady_clock::now())) {
            // 触发心跳发送（使用现有的个人定时器）
            // 这里我们只是触发，不替换现有逻辑
            ++processed;
        }
    }

    // 更新批处理索引
    heartbeat_batch_index_.store((start_index + BATCH_SIZE) % total_sessions);

    if (processed > 0) {
        std::cout << "💓 批处理心跳: 处理了 " << processed << " 个用户" << std::endl;
    }
}

void MiniBoosterSession::process_business_data_batch() {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    size_t total_sessions = connections_.size();
    if (total_sessions == 0) return;

    // 批处理：每次处理一部分用户
    const size_t BATCH_SIZE = 30; // 每批处理30个用户
    size_t start_index = business_batch_index_.load() % total_sessions;
    size_t processed = 0;

    for (size_t i = 0; i < total_sessions && processed < BATCH_SIZE; ++i) {
        size_t index = (start_index + i) % total_sessions;
        auto& session = connections_[index];

        if (session && session->is_active(std::chrono::steady_clock::now())) {
            // 触发业务数据发送（使用现有的个人定时器）
            // 这里我们只是触发，不替换现有逻辑
            ++processed;
        }
    }

    // 更新批处理索引
    business_batch_index_.store((start_index + BATCH_SIZE) % total_sessions);

    if (processed > 0) {
        std::cout << "📤 批处理业务数据: 处理了 " << processed << " 个用户" << std::endl;
    }
}

} // namespace mini_booster