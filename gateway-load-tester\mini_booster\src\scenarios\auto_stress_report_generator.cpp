#include "scenarios/auto_stress_report_generator.h"
#include <iostream>
#include <filesystem>

#ifdef _WIN32
#include <windows.h>
#include <direct.h>
#define mkdir(path, mode) _mkdir(path)
#else
#include <sys/stat.h>
#include <sys/types.h>
#endif

namespace mini_booster {

AutoStressReportGenerator::AutoStressReportGenerator() 
    : output_directory_("reports") {
}

AutoStressReportGenerator::~AutoStressReportGenerator() {
}

std::string AutoStressReportGenerator::generateCSVReport(const AutoStressResult& result, const std::string& file_prefix) {
    if (!result.success || result.batch_history.empty()) {
        std::cerr << "[AutoStressReport] ❌ No valid data to generate CSV report" << std::endl;
        return "";
    }

    // 生成CSV文件名
    std::string filename = generateFileName(file_prefix, ".csv");
    std::string filepath = output_directory_ + "/" + filename;

    // 确保输出目录存在
    if (!ensureDirectoryExists(output_directory_)) {
        std::cerr << "[AutoStressReport] ❌ Failed to create output directory: " << output_directory_ << std::endl;
        return "";
    }

    // 生成CSV内容
    std::ostringstream csv;
    
    // CSV头部
    csv << "batch_number,total_users,successful_connections,failed_connections,total_attempts,online_users,failure_rate_percent,connections_per_second,timestamp_ms\n";
    
    // 数据行
    for (const auto& batch : result.batch_history) {
        auto timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            batch.timestamp.time_since_epoch()).count();
        
        csv << batch.batch_number << ","
            << batch.total_users << ","
            << batch.successful_connections << ","
            << batch.failed_connections << ","
            << batch.total_attempts << ","
            << batch.online_users << ","
            << std::fixed << std::setprecision(2) << batch.failure_rate_percent << ","
            << std::fixed << std::setprecision(1) << batch.connections_per_second << ","
            << timestamp_ms << "\n";
    }

    // 写入文件
    if (writeToFile(filepath, csv.str())) {
        std::cout << "[AutoStressReport] ✅ CSV报告生成成功: " << filepath << std::endl;
        return filepath;
    } else {
        std::cerr << "[AutoStressReport] ❌ CSV报告写入失败: " << filepath << std::endl;
        return "";
    }
}

std::string AutoStressReportGenerator::generateJSONReport(const AutoStressResult& result, const std::string& file_prefix) {
    if (!result.success) {
        std::cerr << "[AutoStressReport] ❌ No valid data to generate JSON report" << std::endl;
        return "";
    }

    // 生成JSON文件名（改为 .txt 文本文件，便于在无 JSON 查看器的环境中打开）
    std::string filename = generateFileName(file_prefix, ".txt");
    std::string filepath = output_directory_ + "/" + filename;

    // 确保输出目录存在
    if (!ensureDirectoryExists(output_directory_)) {
        std::cerr << "[AutoStressReport] ❌ Failed to create output directory: " << output_directory_ << std::endl;
        return "";
    }

    // 生成JSON内容
    std::ostringstream json;
    json << "{\n";
    json << "  \"autostress_report\": {\n";
    json << "    \"summary\": {\n";
    json << "      \"max_concurrent_users\": " << result.max_concurrent_users << ",\n";
    json << "      \"peak_cps\": " << std::fixed << std::setprecision(1) << result.peak_cps << ",\n";
    json << "      \"total_batches\": " << result.total_batches << ",\n";
    json << "      \"test_duration_seconds\": " << std::fixed << std::setprecision(2) 
         << std::chrono::duration<double>(result.end_time - result.start_time).count() << ",\n";
    json << "      \"stop_reason\": \"" << escapeJsonString(result.stop_reason) << "\",\n";
    json << "      \"success\": " << (result.success ? "true" : "false") << ",\n";
    json << "      \"failure_batch\": " << result.failure_batch_number << ",\n";
    json << "      \"failure_rate\": " << std::fixed << std::setprecision(2) << result.failure_rate_at_stop << ",\n";
    json << "      \"io_threads\": " << result.io_thread_count << "\n";
    json << "    },\n";

    // 追加最终统计
    json << "    \"final_stats\": {\n";
    if (result.final_stats) {
        const auto& fs = *result.final_stats;
        json << "      \"successful_handshakes\": " << fs.successful_handshakes << ",\n";
        json << "      \"failed_handshakes\": " << fs.failed_handshakes << ",\n";
        json << "      \"active_control_connections\": " << fs.active_control_connections << ",\n";
        json << "      \"active_data_tunnels\": " << fs.active_data_tunnels << ",\n";
        json << "      \"total_bytes_sent\": " << fs.total_bytes_sent << ",\n";
        json << "      \"total_bytes_received\": " << fs.total_bytes_received << "\n";
    }
    json << "    },\n";
    
    json << "    \"timeline\": {\n";
    json << "      \"start_time_iso\": \"" << formatTimestamp() << "\",\n";
    json << "      \"test_duration\": \"" << formatDuration(result.start_time, result.end_time) << "\"\n";
    json << "    },\n";

    // 🔧 新增：流量统计历史
    json << "    \"traffic_history\": [\n";
    for (size_t i = 0; i < result.traffic_history.size(); ++i) {
        const auto& traffic = result.traffic_history[i];
        json << "      {\n";
        json << "        \"connection_count\": " << traffic.connection_count << ",\n";
        json << "        \"business_bytes_per_second\": " << std::fixed << std::setprecision(1)
             << traffic.business_bytes_per_second << ",\n";
        json << "        \"business_packets_per_second\": " << std::fixed << std::setprecision(1)
             << traffic.business_packets_per_second << ",\n";
        json << "        \"heartbeat_bytes_per_second\": " << std::fixed << std::setprecision(1)
             << traffic.heartbeat_bytes_per_second << ",\n";
        json << "        \"heartbeat_packets_per_second\": " << std::fixed << std::setprecision(1)
             << traffic.heartbeat_packets_per_second << ",\n";
        json << "        \"total_bytes_per_second\": " << std::fixed << std::setprecision(1)
             << traffic.total_bytes_per_second << ",\n";
        json << "        \"cumulative_bytes_sent\": " << traffic.cumulative_bytes_sent << "\n";
        json << "      }";
        if (i < result.traffic_history.size() - 1) {
            json << ",";
        }
        json << "\n";
    }
    json << "    ],\n";

    // 失败曲线数据
    json << "    \"failure_curve\": [\n";
    for (size_t i = 0; i < result.batch_history.size(); ++i) {
        const auto& batch = result.batch_history[i];
        json << "      {\n";
        json << "        \"batch\": " << batch.batch_number << ",\n";
        json << "        \"users\": " << batch.total_users << ",\n";
        json << "        \"success_count\": " << batch.successful_connections << ",\n";
        json << "        \"failure_count\": " << batch.failed_connections << ",\n";
        json << "        \"total_attempts\": " << batch.total_attempts << ",\n";
        json << "        \"online_users\": " << batch.online_users << ",\n";
        json << "        \"failure_rate\": " << std::fixed << std::setprecision(2) 
             << batch.failure_rate_percent << ",\n";
        json << "        \"cps\": " << std::fixed << std::setprecision(1) 
             << batch.connections_per_second << "\n";
        json << "      }";
        if (i < result.batch_history.size() - 1) {
            json << ",";
        }
        json << "\n";
    }
    json << "    ]\n";
    json << "  }\n";
    json << "}\n";

    // 写入文件
    if (writeToFile(filepath, json.str())) {
        std::cout << "[AutoStressReport] ✅ JSON报告生成成功: " << filepath << std::endl;
        return filepath;
    } else {
        std::cerr << "[AutoStressReport] ❌ JSON报告写入失败: " << filepath << std::endl;
        return "";
    }
}

std::string AutoStressReportGenerator::generateTerminalSummary(const AutoStressResult& result) {
    if (!result.success) {
        return "[AutoStressReport] ❌ 测试未成功完成，无法生成摘要";
    }

    std::ostringstream summary;
    
    // 标题
    summary << std::string(70, '=') << "\n";
    summary << "             🚀 自动极限压测结果摘要\n";
    summary << std::string(70, '=') << "\n";
    
    // 核心指标
    summary << "🎯 关键指标:\n";
    summary << "   • 极限并发用户数: " << result.max_concurrent_users << "\n";
    summary << "   • 峰值CPS: " << std::fixed << std::setprecision(0) << result.peak_cps << "/s\n";
    summary << "   • 总批次数: " << result.total_batches << "\n";
    summary << "   • 测试时长: " << formatDuration(result.start_time, result.end_time) << "\n";
    summary << "   • 停止原因: " << result.stop_reason << "\n";
    summary << "   • 触发批次: #" << result.failure_batch_number << ", 失败率 "
            << std::fixed << std::setprecision(1) << result.failure_rate_at_stop << "%\n";
    summary << "   • IO 线程: " << result.io_thread_count << "\n";
    
    // 批次分析
    if (!result.batch_history.empty()) {
        const auto& last_batch = result.batch_history.back();
        summary << "\n📊 最终批次状态:\n";
        summary << "   • 批次编号: #" << last_batch.batch_number << "\n";
        summary << "   • 成功连接: " << last_batch.successful_connections << "\n";
        summary << "   • 失败连接: " << last_batch.failed_connections << "\n";
        summary << "   • 失败率: " << std::fixed << std::setprecision(1) 
                << last_batch.failure_rate_percent << "%\n";
        
        // 趋势分析
        if (result.batch_history.size() >= 3) {
            const auto& first_batch = result.batch_history[0];
            const auto& mid_batch = result.batch_history[result.batch_history.size() / 2];
            
            summary << "\n📈 压测趋势分析:\n";
            summary << "   • 初期失败率: " << std::fixed << std::setprecision(1) 
                    << first_batch.failure_rate_percent << "%\n";
            summary << "   • 中期失败率: " << std::fixed << std::setprecision(1) 
                    << mid_batch.failure_rate_percent << "%\n";
            summary << "   • 最终失败率: " << std::fixed << std::setprecision(1) 
                    << last_batch.failure_rate_percent << "%\n";
        }
    }
    
    summary << "\n" << std::string(70, '=') << "\n";
    
    return summary.str();
}

std::vector<std::string> AutoStressReportGenerator::generateAllReports(const AutoStressResult& result, const std::string& file_prefix) {
    std::vector<std::string> generated_files;
    
    // 生成CSV报告
    std::string csv_file = generateCSVReport(result, file_prefix);
    if (!csv_file.empty()) {
        generated_files.push_back(csv_file);
    }
    
    // 生成JSON报告
    std::string json_file = generateJSONReport(result, file_prefix);
    if (!json_file.empty()) {
        generated_files.push_back(json_file);
    }
    
    // 打印终端摘要
    std::string terminal_summary = generateTerminalSummary(result);
    std::cout << terminal_summary << std::endl;
    
    return generated_files;
}

void AutoStressReportGenerator::setOutputDirectory(const std::string& directory) {
    output_directory_ = directory;
}

// 私有方法实现

std::string AutoStressReportGenerator::generateFileName(const std::string& prefix, const std::string& extension) const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream filename;
    filename << prefix;
    
#ifdef _WIN32
    struct tm tm_buf;
    if (localtime_s(&tm_buf, &time_t) == 0) {
        filename << "_" << std::put_time(&tm_buf, "%Y-%m-%d-%H%M");
    }
#else
    filename << "_" << std::put_time(std::localtime(&time_t), "%Y-%m-%d-%H%M");
#endif
    
    filename << extension;
    return filename.str();
}

std::string AutoStressReportGenerator::formatTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream timestamp;
#ifdef _WIN32
    struct tm tm_buf;
    if (localtime_s(&tm_buf, &time_t) == 0) {
        timestamp << std::put_time(&tm_buf, "%Y-%m-%dT%H:%M:%S");
    }
#else
    timestamp << std::put_time(std::localtime(&time_t), "%Y-%m-%dT%H:%M:%S");
#endif
    
    return timestamp.str();
}

std::string AutoStressReportGenerator::formatDuration(const std::chrono::steady_clock::time_point& start, 
                                                     const std::chrono::steady_clock::time_point& end) const {
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end - start);
    auto seconds = duration.count();
    
    if (seconds < 60) {
        return std::to_string(seconds) + "秒";
    } else if (seconds < 3600) {
        int minutes = seconds / 60;
        int remaining_seconds = seconds % 60;
        return std::to_string(minutes) + "分" + std::to_string(remaining_seconds) + "秒";
    } else {
        int hours = seconds / 3600;
        int remaining_minutes = (seconds % 3600) / 60;
        return std::to_string(hours) + "时" + std::to_string(remaining_minutes) + "分";
    }
}

std::string AutoStressReportGenerator::escapeJsonString(const std::string& input) const {
    std::string result;
    result.reserve(input.size() + 10); // 预留一些空间给转义字符
    
    for (char c : input) {
        switch (c) {
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default: result += c; break;
        }
    }
    
    return result;
}

bool AutoStressReportGenerator::writeToFile(const std::string& filepath, const std::string& content) const {
    try {
        std::ofstream file(filepath);
        if (!file.is_open()) {
            return false;
        }
        
        file << content;
        file.close();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[AutoStressReport] ❌ 文件写入异常: " << e.what() << std::endl;
        return false;
    }
}

bool AutoStressReportGenerator::ensureDirectoryExists(const std::string& directory) const {
    try {
        std::filesystem::path dir_path(directory);
        if (!std::filesystem::exists(dir_path)) {
            return std::filesystem::create_directories(dir_path);
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[AutoStressReport] ❌ 目录创建异常: " << e.what() << std::endl;
        return false;
    }
}

} // namespace mini_booster 