#include "scenarios/auto_stress_runner.h"
#include "../include/error_logger.h"  // 添加ErrorLogger头文件
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cmath>
#include <random>   // jitter for connection pacing
#include <array>

namespace {
    struct IoShard {
        boost::asio::io_context io;
        boost::asio::executor_work_guard<boost::asio::io_context::executor_type> guard{io.get_executor()};
        std::unique_ptr<mini_booster::MiniBoosterSession> session;
        std::vector<std::thread> workers;
        IoShard(): session(std::make_unique<mini_booster::MiniBoosterSession>(io)){
            // two worker threads per shard
            for(int i=0;i<2;++i){
                workers.emplace_back([this]{ io.run(); });
            }
        }
        ~IoShard(){
            guard.reset();
            for(auto& t:workers) if(t.joinable()) t.join();
        }
    };
}

namespace mini_booster {

AutoStressRunner::AutoStressRunner(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , session_(std::make_unique<MiniBoosterSession>(io_context))
    , batch_timer_(std::make_unique<boost::asio::steady_timer>(io_context))
    , stats_timer_(std::make_unique<boost::asio::steady_timer>(io_context))
    , hold_timer_(std::make_unique<boost::asio::steady_timer>(io_context))
    , monitor_timer_(std::make_unique<boost::asio::steady_timer>(io_context))
    , last_stats_time_(std::chrono::steady_clock::now())
    , last_cps_time_(std::chrono::steady_clock::now())
    , ramp_current_batch_size_(10) // 渐进式增压从10开始
    , hold_phase_started_(false)
{
}

AutoStressRunner::~AutoStressRunner() {
    stop();
}

// 添加新的配置方法实现
void AutoStressRunner::set_gateway_info(const std::string& host, uint16_t port) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    gateway_host_ = host;
    gateway_port_ = port;
}

void AutoStressRunner::set_app_id(const std::string& app_id) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    app_id_ = app_id;
}

void AutoStressRunner::set_heartbeat_interval(uint32_t seconds) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    heartbeat_interval_ = seconds;
}

void AutoStressRunner::set_business_data_interval(uint32_t ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    business_data_interval_ = ms;
}

// 新增：设置转发节点(Nginx)信息
void AutoStressRunner::set_node_info(const std::string& host, uint16_t port) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    node_host_ = host;
    node_port_ = port;
}

// 配置会话的统一方法
void AutoStressRunner::configure_session() {
    // 配置MiniBoosterSession
    MiniBoosterConfig session_config;
    
    // 使用最新的网关配置
    session_config.gateway_host = gateway_host_;
    session_config.gateway_port = gateway_port_;
    // 使用转发节点信息（若未显式设置，则保持默认值）
    session_config.node_host = node_host_;
    session_config.node_port = node_port_;
    session_config.app_id = app_id_;
    session_config.users_count = current_users_;
    session_config.start_user_id = base_start_user_id_;
    // 业务链改为只读：关闭主动写
    session_config.packets_per_second = 0.0;          // 不主动发送业务包
    session_config.business_data_interval_ms = 0;     // 亦不定时写
    session_config.packet_size = 1024;                // 仍设置包长，避免后续逻辑依赖
    session_config.enable_heartbeat = true;

    // —— 启用第四数据隧道及随机业务负载 ——
    // 若 YAML 已启用隧道，则沿用其随机负载与包长设置；
    // AutoStress 仅确保隧道处于开启状态，不再硬编码覆盖包长区间。
    session_config.enable_data_tunnel   = true;
    session_config.tunnel_random_payload = true;
 
    // 重要：应用特定的优化配置 - 针对400连接瓶颈优化
    session_config.heartbeat_interval_seconds = std::max(8U, heartbeat_interval_ * 2); // 至少8秒，减少心跳频率
    session_config.business_data_interval_ms = std::max(1000U, business_data_interval_ * 2); // 至少1秒，减少业务定时器
    
    // 应用配置
    session_->configure(session_config);
}

void AutoStressRunner::configure(const AutoStressConfig& config, uint32_t start_user_id, uint32_t initial_users) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    config_ = config;
    base_start_user_id_ = start_user_id;
    initial_users_ = initial_users;
    current_users_ = initial_users;
    
    // 使用统一的会话配置方法
    configure_session();
    
    // 设置渐进式增压初始参数
    if (config_.enable_gradual_ramp) {
        gradual_ramp_active_ = true;
        ramp_current_batch_size_ = config_.min_batch_connections;
    } else {
        gradual_ramp_active_ = false;
        ramp_current_batch_size_ = config_.batch_size;
    }
    
    std::cout << "[AutoStressRunner] Configured: batch=" << config_.batch_size 
              << " interval=" << config_.interval_seconds 
              << "s threshold=" << config_.failure_threshold_percent 
              << "% max=" << config_.max_users 
              << (config_.enable_gradual_ramp ? " (gradual-ramp)" : "") << std::endl;
}

bool AutoStressRunner::start() {
    if (running_.exchange(true)) {
        std::cout << "[AutoStressRunner] Already running" << std::endl;
        return false;
    }
    
    std::cout << "==========================================" << std::endl;
    std::cout << "🚀 [AutoStress] ▶ 自动极限压测开始" << std::endl;
    std::cout << "   网关: " << gateway_host_ << ":" << gateway_port_
              << " AppID: " << app_id_ << std::endl;
    std::cout << "   batch=" << config_.batch_size 
              << " interval=" << config_.interval_seconds 
              << "s threshold=" << config_.failure_threshold_percent 
              << "% (max " << config_.max_users << ")" << std::endl;
    std::cout << "   心跳间隔=" << heartbeat_interval_
              << "s 业务数据间隔=" << business_data_interval_ << "ms" << std::endl;
    std::cout << "==========================================" << std::endl;
    
    // 初始化结果结构
    result_ = AutoStressResult{};
    result_.start_time = std::chrono::steady_clock::now();
    current_batch_number_ = 0;
    current_users_ = initial_users_;
    last_successful_connections_ = 0;
    
    // 确保会话配置最新
    configure_session();
    
    // 启动会话
    if (!session_->start()) {
        std::cout << "❌ [AutoStress] Failed to start session" << std::endl;
        running_.store(false);
        return false;
    }
    
    // 启动统计定时器
    stats_timer_->expires_after(std::chrono::seconds(config_.stats_period_seconds));
    stats_timer_->async_wait([this](const boost::system::error_code& ec) {
        on_stats_timer(ec);
    });
    
    // 如果已经存在初始用户，则等待一个间隔后再执行首批增压，避免启动瞬间翻倍
    if (initial_users_ > 0) {
        schedule_next_batch();
    } else {
        // 无初始用户时立即执行第一批
        execute_batch();
    }
    
    return true;
}

void AutoStressRunner::stop() {
    if (!running_.exchange(false)) {
        return;
    }
    
    // 取消定时器
    if (batch_timer_) {
        batch_timer_->cancel();
    }
    if (stats_timer_) {
        stats_timer_->cancel();
    }
    if (hold_timer_) {
        hold_timer_->cancel();
    }
    if (monitor_timer_) {
        monitor_timer_->cancel();
    }
    
    // 记录最后一批统计
    record_batch_completion();

    // 先生成报告，让用户立刻看到结果
    finalize_test("Manual stop");

    // 确保彻底关闭所有底层连接，防止继续发送心跳/数据
    if (session_ && session_->is_running()) {
        std::cout << "[AutoStress] 正在关闭所有连接..." << std::endl;
        
        // 停止会话
        session_->stop();
        
        // 等待连接关闭 (最多等待5秒)
        for (int i = 0; i < 10; i++) {
            auto stats = session_->get_stats();
            if (stats.active_control_connections == 0 && 
                stats.active_business_connections == 0 && 
                stats.active_data_tunnels == 0) {
                std::cout << "[AutoStress] 所有连接已关闭" << std::endl;
                break;
            }
            
            std::cout << "[AutoStress] 等待连接关闭: " 
                      << stats.active_control_connections << " 控制连接, "
                      << stats.active_business_connections << " 业务连接, "
                      << stats.active_data_tunnels << " 数据隧道" << std::endl;
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
}

bool AutoStressRunner::is_running() const {
    return running_.load();
}

void AutoStressRunner::execute_batch() {
    if (!running_.load()) {
        return;
    }
    
    if (current_batch_number_ > 0) {
        record_batch_completion();
    }
    
    ++current_batch_number_;
    size_t batch_size = calculate_next_batch_size();
    
    // 检查是否超过最大用户数
    if (current_users_ + batch_size > config_.max_users) {
        batch_size = config_.max_users - current_users_;
        if (batch_size == 0) {
            finalize_test("Reached max users limit");
            return;
        }
    }
    
    current_users_ += batch_size; // 目标并发

    // 使用配置会话方法更新会话配置
    configure_session();

    // === 创建/分配 shard 容器 (静态) ===
    static const int SHARD_CNT = 4;
    static std::array<IoShard, SHARD_CNT> shards; // one-off static

    // 为所有 shard 配置 session（只在第一次进入时执行）
    static bool shards_configured = false;
    if(!shards_configured){
        // 启动每个 shard 的 io_context 工作线程
        for(auto& s:shards){
            // session 将在后续 scale_up 时按需配置
            s.session->start();
        }
        shards_configured = true;
    }

    // 扩展会话连接数——优化分块策略减少阻塞时间
    const size_t kSubBatch   = 8;   // 从5增加到8条连接，减少批次总数
    const int    kDelayMs    = 80; // 从150ms减少到80ms，减少总阻塞时间

    // === 单连接随机抖动生成器 - 优化范围减少总阻塞时间 ===
    static std::mt19937 rng{ std::random_device{}() };
    std::uniform_int_distribution<int> jitter_ms(10, 40); // 从30-120ms优化到10-40ms

    size_t remain = batch_size;
    while (remain > 0) {
        size_t chunk = std::min(remain, kSubBatch);
        if (!session_->scale_up(static_cast<int>(chunk))) {
        std::cout << "❌ [AutoStress] Failed to scale up connections" << std::endl;
        finalize_test("Scale up failed");
        return;
        }
        remain -= chunk;

        // 对本 sub-batch 内的每条连接加入抖动 - 优化：只为部分连接添加抖动
        if (chunk > 1) { // 只有多连接时才添加抖动
            for (size_t i = 1; i < chunk && remain + chunk > 0; ++i) { // 跳过第一个连接
                std::this_thread::sleep_for(std::chrono::milliseconds(jitter_ms(rng)));
            }
        }

        if (remain > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(kDelayMs));
        }
    }

    // 更新当前批次统计
    update_current_batch_stats();
    
    if (config_.verbose_output) {
        auto stats = session_->get_stats();
        double failure_rate = calculate_failure_rate();
        
        std::cout << "[AutoStress] +" << batch_size 
                  << " target=" << current_users_ 
                  << " online=" << stats.active_control_connections 
                  << " ok=" << stats.successful_handshakes 
                  << " fail=" << stats.failed_handshakes;
        
        if (stats.total_handshakes_sent > 0) {
            std::cout << " (失败率 " << std::fixed << std::setprecision(1) 
                      << failure_rate << "%)";
        }
        std::cout << std::endl;
    }
    
    // 检查失败率阈值
    check_failure_threshold();
    
    // 如果测试仍在运行，调度下一批次
    if (running_.load() && current_users_ < config_.max_users) {
        schedule_next_batch();
    } else if (current_users_ >= config_.max_users && !hold_phase_started_) {
        // 启动监视器等待所有握手完成或失败
        monitor_check();
    }
}

void AutoStressRunner::schedule_next_batch() {
    if (!running_.load()) {
        return;
    }
    
    batch_timer_->expires_after(std::chrono::seconds(config_.interval_seconds));
    batch_timer_->async_wait([this](const boost::system::error_code& ec) {
        on_batch_timer(ec);
    });
}

void AutoStressRunner::on_batch_timer(const boost::system::error_code& ec) {
    if (ec == boost::asio::error::operation_aborted) {
        return; // 定时器被取消
    }
    
    if (ec) {
        std::cout << "❌ [AutoStress] Batch timer error: " << ec.message() << std::endl;
        finalize_test("Timer error");
        return;
    }
    
    execute_batch();
}

void AutoStressRunner::on_stats_timer(const boost::system::error_code& ec) {
    if (ec == boost::asio::error::operation_aborted) {
        return;
    }
    
    if (ec) {
        std::cout << "❌ [AutoStress] Stats timer error: " << ec.message() << std::endl;
        return;
    }
    
    // 打印周期性统计
    if (config_.verbose_output && running_.load()) {
        auto stats = session_->get_stats();
        double cps = calculate_current_cps();
        
        std::cout << "[AutoStress] 📊 Stats: users=" << current_users_ 
                  << " cps=" << std::fixed << std::setprecision(1) << cps 
                  << "/s success=" << stats.successful_handshakes 
                  << " fail=" << stats.failed_handshakes << std::endl;
    }
    
    // 计算并记录活跃会话数量（写入日志，不打印控制台）
    size_t active_conn = session_->count_active_sessions();
    double cps_now = calculate_current_cps();
    ErrorLogger::instance().log_debug("[STAT] total=" + std::to_string(current_users_) +
                                     " active=" + std::to_string(active_conn) +
                                     " cps=" + std::to_string(cps_now));

    // 额外写入 metrics 日志，简化后期分析
    ErrorLogger::instance().log_metric("[STAT] total=" + std::to_string(current_users_) +
                                      " active=" + std::to_string(active_conn) +
                                      " cps=" + std::to_string(cps_now));

    // 🔧 记录流量快照到测试结果中
    if (session_) {
        session_->record_traffic_snapshot(result_);
    }

    // 重新调度统计定时器
    if (running_.load()) {
        stats_timer_->expires_after(std::chrono::seconds(config_.stats_period_seconds));
        stats_timer_->async_wait([this](const boost::system::error_code& ec) {
            on_stats_timer(ec);
        });
    }
}

void AutoStressRunner::check_failure_threshold() {
    auto stats = session_->get_stats();
    
    // 检查连接是否全部断开
    if (stats.active_control_connections == 0 &&
        stats.active_business_connections == 0 &&
        stats.total_handshakes_sent > 0) {
        std::cout << "[AutoStress] 所有连接已断开，测试结束" << std::endl;
        finalize_test("All connections lost");
        return;
    }
    
    // 检查握手超时数量
    if (stats.timeout_handshakes > 0) {
        double timeout_rate = (static_cast<double>(stats.timeout_handshakes) / 
                              stats.total_handshakes_sent) * 100.0;
        
        // 如果超时率超过5%，提前结束测试
        if (timeout_rate >= 5.0) {
            std::cout << "[AutoStress] 握手超时率 " << std::fixed << std::setprecision(1) 
                      << timeout_rate << "% 超过阈值 5%，测试结束" << std::endl;
            finalize_test("Handshake timeout threshold exceeded");
            return;
        }
    }
    
    // 检查失败率
    size_t attempted = stats.successful_handshakes + stats.failed_handshakes;
    
    // 只有在已完成握手数达到最小阈值后才评估失败率，
    // 提前发现"刚开始就大量失败"的情况。
    if (attempted < config_.min_batch_connections) {
        return; // 数据太少，暂不判断
    }

    double failure_rate = calculate_failure_rate();
    
    // 添加详细的失败率检查日志
    ErrorLogger::instance().log_info("失败率检查: 成功握手=" + std::to_string(stats.successful_handshakes) + 
                                   ", 失败握手=" + std::to_string(stats.failed_handshakes) + 
                                   ", 总握手=" + std::to_string(stats.total_handshakes_sent) + 
                                   ", 失败率=" + std::to_string(failure_rate) + "%" +
                                   ", 阈值=" + std::to_string(config_.failure_threshold_percent) + "%");

    if (failure_rate >= config_.failure_threshold_percent) {
        std::cout << "[AutoStress] ❌ 触发阈值，测试自动结束" << std::endl;
        std::cout << "   失败率 " << std::fixed << std::setprecision(1) 
                  << failure_rate << "% ≥ 阈值 " 
                  << config_.failure_threshold_percent << "%" << std::endl;
        
        // 记录失败率超过阈值的详细日志
        ErrorLogger::instance().log_error("失败率超过阈值: 当前=" + std::to_string(failure_rate) + 
                                        "%, 阈值=" + std::to_string(config_.failure_threshold_percent) + 
                                        "%, 批次=" + std::to_string(current_batch_number_) + 
                                        ", 用户数=" + std::to_string(current_users_));
        
        finalize_test("Failure threshold exceeded");
    }
}

void AutoStressRunner::finalize_test(const std::string& stop_reason) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (result_.success) {
        return; // 已经结束过了
    }
    
    result_.end_time = std::chrono::steady_clock::now();
    result_.total_batches = current_batch_number_;
    result_.max_concurrent_users = session_ ? session_->get_stats().successful_handshakes : current_users_;
    result_.peak_cps = calculate_current_cps();
    result_.stop_reason = stop_reason;
    result_.success = true;

    // 新增字段填充
    result_.failure_batch_number = current_batch_number_;
    result_.failure_rate_at_stop = calculate_failure_rate();
    result_.io_thread_count = std::thread::hardware_concurrency();

    // 停止时完整统计
    if (session_) {
        result_.final_stats = std::make_unique<MiniBoosterStats>(session_->get_stats());
    }
    
    // 结束后将运行标志置为 false，防止后续调度
    running_.store(false);
    
    // 取消可能还在等待的定时器
    if (batch_timer_) {
        batch_timer_->cancel();
    }
    if (stats_timer_) {
        stats_timer_->cancel();
    }
    if (hold_timer_) {
        hold_timer_->cancel();
    }
    if (monitor_timer_) {
        monitor_timer_->cancel();
    }
    
    // 记录最后一批统计
    record_batch_completion();
    
    // ⚠️ 生成报告完成后，再异步关闭所有连接，防止 CMD5 日志刷屏遮挡报告
    if (session_ && session_->is_running()) {
        boost::asio::post(io_context_, [sess = session_.get()]{
            sess->stop();
        });
    }
    
    auto stats = session_ ? session_->get_stats() : MiniBoosterStats{};
    double duration_seconds = std::chrono::duration<double>(result_.end_time - result_.start_time).count();
    
    std::cout << "[AutoStress] ✅ 极限并发 " << result_.max_concurrent_users 
              << "  峰值CPS " << std::fixed << std::setprecision(0) << result_.peak_cps << "/s" << std::endl;
    std::cout << "[AutoStress] 📊 总时长 " << std::fixed << std::setprecision(1) 
              << duration_seconds << "s  总批次 " << result_.total_batches 
              << "  原因: " << stop_reason << std::endl;
    
    if (config_.auto_generate_reports) {
        // P5-C: 实现报告生成
        std::cout << "[AutoStress] 📄 正在生成报告..." << std::endl;
        
        auto report_generator = std::make_unique<AutoStressReportGenerator>();
        report_generator->setOutputDirectory("reports");
        
        auto generated_files = report_generator->generateAllReports(result_, config_.report_prefix);
        
        if (!generated_files.empty()) {
            std::cout << "[AutoStress] 📄 报告: ";
            for (size_t i = 0; i < generated_files.size(); ++i) {
                std::cout << generated_files[i];
                if (i < generated_files.size() - 1) {
                    std::cout << " / ";
                }
            }
            std::cout << std::endl;
        }
    }
}

double AutoStressRunner::calculate_failure_rate() const {
    auto stats = session_->get_stats();
    uint64_t attempts = stats.total_handshakes_sent;
    // 某些历史版本未正确维护 total_handshakes_sent，回退到成功+失败计数
    if (attempts == 0) {
        attempts = stats.successful_handshakes + stats.failed_handshakes;
    }
    if (attempts == 0) {
        return 0.0;
    }
    return (static_cast<double>(stats.failed_handshakes) / attempts) * 100.0;
}

double AutoStressRunner::calculate_current_cps() const {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration<double>(now - last_cps_time_).count();
    
    if (duration <= 0.0) {
        return 0.0;
    }
    
    auto stats = session_->get_stats();
    size_t new_connections = stats.successful_handshakes - last_successful_connections_;
    
    return new_connections / duration;
}

// ================= 新增: 监控批次完成 =================
void AutoStressRunner::monitor_check() {
    if (!running_.load()) {
        return;
    }
    auto stats = session_->get_stats();
    // ① 成功数已满，进入 hold 阶段（record_batch_completion 内部会触发 finalize_test 或 hold 定时）
    if (stats.successful_handshakes >= config_.max_users) {
        record_batch_completion();
        return;
    }
    // ② 所有尝试均已结束（成功+失败 == max_users）
    if (stats.successful_handshakes + stats.failed_handshakes >= config_.max_users) {
        finalize_test("Completed (all attempts finished)");
        return;
    }
    // ③ 继续等待
    monitor_timer_->expires_after(std::chrono::seconds(1));
    monitor_timer_->async_wait([this](const boost::system::error_code& ec) {
        if (ec == boost::asio::error::operation_aborted) return;
        monitor_check();
    });
}

void AutoStressRunner::update_current_batch_stats() {
    auto now = std::chrono::steady_clock::now();
    auto stats = session_->get_stats();
    
    // 更新CPS计算基准
    last_cps_time_ = now;
    last_successful_connections_ = stats.successful_handshakes;
}

void AutoStressRunner::record_batch_completion() {
    AutoStressBatchStats batch_stats;
    batch_stats.batch_number = current_batch_number_;
    batch_stats.total_users = current_users_;
    batch_stats.timestamp = std::chrono::steady_clock::now();
    
    auto stats = session_->get_stats();
    batch_stats.successful_connections = stats.successful_handshakes;
    batch_stats.failed_connections = stats.failed_handshakes;
    batch_stats.total_attempts = stats.total_handshakes_sent;
    batch_stats.online_users = stats.active_control_connections; // 活跃控制连接数即在线
    batch_stats.failure_rate_percent = calculate_failure_rate();
    batch_stats.connections_per_second = calculate_current_cps();
    
    result_.batch_history.push_back(batch_stats);

    // 检查是否达到最大并发（全部握手完成后）
    if (!hold_phase_started_ && batch_stats.successful_connections >= config_.max_users) {
        hold_phase_started_ = true;
        std::cout << "[AutoStress] 🎯 已成功建立 " << batch_stats.successful_connections
                  << " 条连接，达到目标并发，开始保持 " << config_.hold_duration_seconds << "s ..." << std::endl;
        if (config_.hold_duration_seconds == 0) {
            finalize_test("Reached max users (no hold)");
        } else {
            hold_timer_->expires_after(std::chrono::seconds(config_.hold_duration_seconds));
            hold_timer_->async_wait([this](const boost::system::error_code& ec){
                if (ec == boost::asio::error::operation_aborted) return;
                finalize_test("Hold phase complete");
            });
        }
    }
}

size_t AutoStressRunner::calculate_next_batch_size() {
    if (!config_.enable_gradual_ramp || !gradual_ramp_active_) {
        return config_.batch_size;
    }
    
    // 渐进式增压逻辑：从min_batch_connections开始，逐步增加到batch_size
    size_t target_batch = config_.batch_size;
    size_t min_batch = config_.min_batch_connections;
    
    if (ramp_current_batch_size_ >= target_batch) {
        gradual_ramp_active_ = false;
        return target_batch;
    }
    
    // 每批次增加20%，但不超过目标值
    size_t next_size = ramp_current_batch_size_ + (ramp_current_batch_size_ * 20 / 100);
    next_size = std::max(next_size, min_batch);
    next_size = std::min(next_size, target_batch);
    
    ramp_current_batch_size_ = next_size;
    return next_size;
}

bool AutoStressRunner::should_use_gradual_ramp() const {
    return config_.enable_gradual_ramp && gradual_ramp_active_;
}

// 状态查询接口实现
AutoStressResult AutoStressRunner::get_result() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return result_;
}

AutoStressBatchStats AutoStressRunner::get_current_batch() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    AutoStressBatchStats current;
    current.batch_number = current_batch_number_;
    current.total_users = current_users_;
    current.timestamp = std::chrono::steady_clock::now();
    
    if (session_) {
        auto stats = session_->get_stats();
        current.successful_connections = stats.successful_handshakes;
        current.failed_connections = stats.failed_handshakes;
        current.total_attempts = stats.total_handshakes_sent;
        current.online_users = stats.active_control_connections; // 活跃控制连接数即在线
        current.failure_rate_percent = calculate_failure_rate();
        current.connections_per_second = calculate_current_cps();
    }
    
    return current;
}

size_t AutoStressRunner::get_current_users() const {
    return current_users_;
}

double AutoStressRunner::get_current_failure_rate() const {
    return calculate_failure_rate();
}

size_t AutoStressRunner::get_batch_number() const {
    return current_batch_number_;
}

} // namespace mini_booster 