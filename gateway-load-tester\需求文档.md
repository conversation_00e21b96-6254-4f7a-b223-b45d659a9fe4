# 龙盾网关压力测试需求文档

## 🎯 测试目标
开发独立的测试客户端和测试服务器，能够直接通信，同时预留与龙盾客户端集成的接口，最终目标是通过龙盾客户端和网关转发来测试网关的极限连接数。

## 🏗️ 测试链路设计

### 当前阶段：直连模式
```
[测试客户端1] ─┐
[测试客户端2] ─┼─> [测试服务器]
[测试客户端N] ─┘
```

### 未来目标：龙盾转发模式
```
[测试客户端1] ─┐
[测试客户端2] ─┼─> [改造后的龙盾客户端] ─> [网关] ─> [测试服务器]
[测试客户端N] ─┘     (多连接支持)      (CcBoosterGate)
```

## 📋 第一阶段：基础测试框架

### 1.1 测试客户端 (Mock Game Client)
**核心功能**：
- 模拟真实游戏客户端的网络行为
- 支持大量并发连接（单进程 1000+ 连接）
- 可配置的数据发送模式和频率
- 详细的性能统计和监控

**技术实现**：
```cpp
class MockGameClient {
private:
    boost::asio::io_context io_context_;
    std::vector<std::unique_ptr<ClientConnection>> connections_;
    std::unique_ptr<PerformanceMonitor> monitor_;
    TestClientConfig config_;
    
public:
    bool start();
    void createConnections(uint32_t count);
    void sendTestData();
    PerformanceMetrics getMetrics();
    void stop();
};

struct TestClientConfig {
    std::string target_host = "127.0.0.1";      // 目标服务器IP
    uint16_t target_port = 8080;                // 目标服务器端口
    uint32_t concurrent_connections = 1000;     // 并发连接数
    uint32_t ramp_up_time_seconds = 30;         // 连接建立时间
    uint32_t test_duration_seconds = 300;       // 测试持续时间
    uint32_t packet_size = 1024;                // 数据包大小
    uint32_t send_interval_ms = 1000;           // 发送间隔
    std::string test_data_pattern = "random";   // 数据模式: random/fixed/incremental
};
```

### 1.2 测试服务器 (Mock Game Server)
**核心功能**：
- 高并发连接处理（支持 10000+ 连接）
- Echo 模式数据回显
- 连接状态监控和统计
- 可配置的响应延迟和处理模式

**技术实现**：
```cpp
class MockGameServer {
private:
    boost::asio::io_context io_context_;
    boost::asio::ip::tcp::acceptor acceptor_;
    std::vector<std::unique_ptr<ServerConnection>> connections_;
    std::unique_ptr<ConnectionManager> connection_manager_;
    TestServerConfig config_;
    
public:
    bool start();
    void acceptConnections();
    PerformanceMetrics getMetrics();
    void stop();
};

struct TestServerConfig {
    std::string listen_host = "0.0.0.0";
    uint16_t listen_port = 8080;
    uint32_t max_connections = 10000;
    bool echo_mode = true;                      // 是否回显数据
    uint32_t response_delay_ms = 0;            // 响应延迟
    uint32_t io_thread_count = 4;              // IO线程数
    bool enable_statistics = true;             // 是否启用统计
};
```

### 1.3 性能监控系统
```cpp
struct PerformanceMetrics {
    // 连接统计
    uint64_t total_connections_attempted = 0;
    uint64_t total_connections_succeeded = 0;
    uint64_t total_connections_failed = 0;
    uint64_t current_active_connections = 0;
    
    // 数据统计
    uint64_t total_bytes_sent = 0;
    uint64_t total_bytes_received = 0;
    uint64_t total_packets_sent = 0;
    uint64_t total_packets_received = 0;
    
    // 性能统计
    double average_response_time_ms = 0.0;
    double p95_response_time_ms = 0.0;
    double p99_response_time_ms = 0.0;
    double current_cps = 0.0;                  // 当前连接建立速率
    double current_throughput_mbps = 0.0;      // 当前吞吐量
    
    // 时间统计
    std::chrono::steady_clock::time_point test_start_time;
    std::chrono::steady_clock::time_point last_update_time;
};
```

## 🔧 龙盾客户端改造分析

### 当前龙盾架构限制分析

基于对源码的深入分析，当前龙盾客户端存在以下限制：

#### 1. 单一控制连接限制
```cpp
// RuleClient.cpp - 当前实现
class RuleClient {
    UINT m_nClientId = 0;        // 只维护一个控制连接ID
    std::wstring m_sServer;      // 单一网关地址
    USHORT m_nServerPort;        // 单一网关端口
};
```

#### 2. 单一用户标识限制
```cpp
// 全局用户信息 - 当前实现
struct GlobalClient {
    UINT nUserId = 0;                    // 单一UserId
    std::wstring strAppID;               // 单一AppID
    std::vector<ListenIp> arrListenIp;   // 监听配置
};
```

#### 3. 会话管理限制
```cpp
// GameClient.cpp - 当前会话管理
class GameClient {
    std::map<UINT, SessionInfo> m_MapSession;  // SessionId -> SessionInfo
    // 所有Session共享同一个UserId
};
```

### 龙盾客户端改造方案

#### 方案一：多实例进程模式（推荐）
**思路**：启动多个龙盾客户端进程，每个进程使用不同的配置

**优势**：
- 改动最小，风险最低
- 进程隔离，稳定性好
- 易于调试和监控

**实现**：
```cpp
// 进程管理器
class DragonShieldProcessManager {
private:
    struct InstanceConfig {
        uint32_t instance_id;
        std::string app_id;
        std::string listen_ip;
        uint16_t listen_port;
        std::string gateway_host;
        uint16_t gateway_port;
    };
    
    std::vector<InstanceConfig> instances_;
    std::vector<HANDLE> process_handles_;
    
public:
    bool startInstances(const std::vector<InstanceConfig>& configs);
    bool stopAllInstances();
    std::vector<PerformanceMetrics> getInstanceMetrics();
};

// 配置文件格式
{
  "gateway": {
    "host": "*************",
    "rule_port": 9001,
    "game_port": 9002
  },
  "instances": [
    {
      "instance_id": 1,
      "app_id": "8888",
      "listen_ip": "127.0.0.1",
      "listen_port": 18888,
      "process_args": "--app-id=8888 --listen-port=18888"
    },
    {
      "instance_id": 2,
      "app_id": "8889",
      "listen_ip": "127.0.0.1", 
      "listen_port": 18889,
      "process_args": "--app-id=8889 --listen-port=18889"
    }
  ]
}
```

#### 方案二：单进程多实例模式
**思路**：在单个进程内模拟多个龙盾实例

**实现要点**：
```cpp
// 虚拟实例管理
class VirtualDragonInstance {
private:
    uint32_t instance_id_;
    std::string app_id_;
    std::unique_ptr<RuleClient> rule_client_;
    std::unique_ptr<GameClient> game_client_;
    uint16_t listen_port_;
    
public:
    bool start(const InstanceConfig& config);
    bool stop();
    ConnectionStats getStats();
};

class MultiInstanceDragonShield {
private:
    std::vector<std::unique_ptr<VirtualDragonInstance>> instances_;
    boost::asio::io_context io_context_;
    
public:
    bool createInstance(const InstanceConfig& config);
    bool startAllInstances();
    void run();
};
```

**需要修改的核心组件**：

1. **RuleClient 改造**：
```cpp
class MultiRuleClient {
private:
    struct InstanceData {
        UINT client_id;
        std::string app_id;
        UINT user_id;
        bool is_logged_in;
    };
    
    std::map<uint32_t, InstanceData> instances_;  // instance_id -> data
    
public:
    bool createInstance(uint32_t instance_id, const std::string& app_id);
    bool loginInstance(uint32_t instance_id);
};
```

2. **GameClient 改造**：
```cpp
class MultiGameClient {
private:
    struct InstanceSession {
        uint32_t instance_id;
        UINT user_id;
        std::map<UINT, SessionInfo> sessions;
    };
    
    std::map<uint32_t, InstanceSession> instance_sessions_;
    
public:
    bool createInstanceListener(uint32_t instance_id, uint16_t port);
    void routeToInstance(UINT client_id, uint32_t instance_id);
};
```

### 改造实施建议

#### 阶段性改造计划：

1. **配置系统扩展**（1-2天）
   - 支持多实例配置文件
   - 命令行参数解析增强

2. **进程管理模块**（2-3天）
   - 进程启动和监控
   - 进程间通信（可选）

3. **测试验证**（1-2天）
   - 多实例功能测试
   - 性能基准测试

#### 最小改动方案：
如果不想大幅修改现有代码，可以采用**配置文件驱动**的方式：

```cpp
// 只需修改 main 函数和配置加载
int main(int argc, char* argv[]) {
    // 从命令行或环境变量读取实例配置
    std::string app_id = GetConfigValue("APP_ID", "8888");
    uint16_t listen_port = GetConfigValue("LISTEN_PORT", 8888);
    
    // 其余代码保持不变
    // ...
}
```

然后通过脚本启动多个进程：
```bash
# 启动多个实例
start CcBooster.exe --app-id=8888 --listen-port=18888
start CcBooster.exe --app-id=8889 --listen-port=18889
start CcBooster.exe --app-id=8890 --listen-port=18890
```

## 🛠️ 技术选型

### 核心技术栈
- **语言**：C++17
- **网络库**：Boost.Asio 1.80+
- **日志库**：spdlog
- **配置库**：nlohmann/json
- **构建系统**：CMake + vcpkg
- **测试框架**：Google Test

### 架构模式
- **异步IO**：基于 Boost.Asio 的事件驱动
- **对象池**：连接对象复用，减少内存分配
- **生产者-消费者**：数据处理队列
- **观察者模式**：性能监控和事件通知

## 🎮 使用场景

### 直连测试模式
```bash
# 启动测试服务器
./mock_server --port=8080 --max-connections=10000

# 启动测试客户端
./mock_client --host=127.0.0.1 --port=8080 --connections=5000 --duration=300
```

### 龙盾转发测试模式（未来）
```bash
# 1. 启动测试服务器
./mock_server --port=8080

# 2. 启动多个龙盾实例
./start_dragon_instances.bat

# 3. 启动测试客户端连接到龙盾
./mock_client --host=127.0.0.1 --port=18888 --connections=1000
./mock_client --host=127.0.0.1 --port=18889 --connections=1000
./mock_client --host=127.0.0.1 --port=18890 --connections=1000
```

## 📊 预留接口设计

为了方便后续与龙盾客户端集成，设计以下抽象接口：

```cpp
// 传输层抽象
class ITransportLayer {
public:
    virtual ~ITransportLayer() = default;
    virtual bool connect(const std::string& host, uint16_t port) = 0;
    virtual bool send(const std::vector<uint8_t>& data) = 0;
    virtual void setReceiveCallback(std::function<void(const std::vector<uint8_t>&)> callback) = 0;
    virtual void disconnect() = 0;
};

// 直连实现
class DirectTransport : public ITransportLayer {
    // 直接TCP连接实现
};

// 龙盾转发实现（未来）
class DragonShieldTransport : public ITransportLayer {
    // 通过龙盾客户端转发的实现
};

// 测试客户端使用接口
class MockGameClient {
private:
    std::unique_ptr<ITransportLayer> transport_;
    
public:
    void setTransport(std::unique_ptr<ITransportLayer> transport) {
        transport_ = std::move(transport);
    }
};
```

## 📝 总结

本需求文档专注于第一阶段的基础测试框架开发，同时深入分析了龙盾客户端的改造方案。通过抽象接口设计，确保测试框架能够无缝切换between直连模式和龙盾转发模式。

**当前优先级**：
1. 实现独立的测试客户端和服务器
2. 验证高并发连接和数据传输功能
3. 建立性能监控和统计系统
4. 为龙盾集成预留清晰的接口

**龙盾改造建议**：采用多进程实例模式，通过配置文件和脚本管理，最小化代码改动风险。
