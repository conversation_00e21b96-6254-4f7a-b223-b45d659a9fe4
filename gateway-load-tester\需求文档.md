# 龙盾网关压力测试需求文档

## 🎯 测试目标
开发轻量级的测试客户端和测试服务器，专注于高并发连接和数据传输测试，同时预留与龙盾客户端集成的接口，最终目标是通过龙盾客户端和网关转发来测试网关的极限连接数和性能表现。

## 🏗️ 测试链路设计

### 当前阶段：直连模式
```
[测试客户端1] ─┐
[测试客户端2] ─┼─> [测试服务器]
[测试客户端N] ─┘
```

### 未来目标：龙盾转发模式
```
[测试客户端1] ─┐
[测试客户端2] ─┼─> [龙盾客户端] ─> [网关] ─> [测试服务器]
[测试客户端N] ─┘    (CcBooster)   (CcBoosterGate)
```

## 📋 第一阶段：基础测试框架

### 1.1 测试客户端 (Mock Game Client)

#### 核心功能
- **连接管理**：建立TCP连接，处理连接断开和重连
- **轻量级通信**：发送简单测试数据包，接收服务器响应
- **高并发支持**：单进程支持大量并发连接（优化内存和CPU使用）
- **性能监控**：实时统计连接状态和性能指标

#### 简化通信协议
为了确保高并发性能，采用极简协议：

**1. 连接状态管理**
```cpp
class ClientConnection {
public:
    enum class State {
        Disconnected,
        Connected,
        Active
    };
    
private:
    State state_ = State::Disconnected;
    uint32_t connection_id_;
    std::chrono::steady_clock::time_point last_activity_;
    boost::asio::ip::tcp::socket socket_;
};
```

**2. 简化数据包格式**
```cpp
// 通用数据包头部（最小开销）
struct PacketHeader {
    uint32_t packet_type;    // 包类型
    uint32_t packet_length;  // 包长度
    uint32_t connection_id;  // 连接ID
    uint32_t sequence_num;   // 序列号
};

// 心跳包（16字节）
struct HeartbeatPacket {
    PacketHeader header;
    uint32_t timestamp;
};

// 数据包（可变长度）
struct DataPacket {
    PacketHeader header;
    uint8_t data[];  // 测试数据
};
```

**3. 客户端行为简化**
```cpp
class MockGameClient {
private:
    struct ClientConfig {
        uint32_t heartbeat_interval_ms = 30000;    // 心跳间隔
        uint32_t data_send_interval_ms = 5000;     // 数据发送间隔
        uint32_t packet_size = 256;                // 固定包大小
        bool enable_heartbeat = true;              // 是否启用心跳
        bool enable_data_send = true;              // 是否发送数据
    };
    
    std::vector<std::unique_ptr<ClientConnection>> connections_;
    ClientConfig config_;
    boost::asio::io_context io_context_;
    
public:
    // 简化的核心方法
    bool createConnections(uint32_t count);
    void startHeartbeat();
    void startDataSend();
    void handleReceive(ClientConnection& conn, const std::vector<uint8_t>& data);
};
```

#### 高并发优化设计
```cpp
class MockGameClient {
private:
    // 使用对象池减少内存分配
    std::unique_ptr<ConnectionPool> connection_pool_;
    
    // 使用环形缓冲区减少内存拷贝
    std::unique_ptr<RingBuffer> send_buffer_;
    std::unique_ptr<RingBuffer> recv_buffer_;
    
    // 最小化线程数量
    boost::asio::io_context io_context_;
    std::vector<std::thread> worker_threads_;  // 通常2-4个线程
    
    // 批量操作减少系统调用
    void batchSendData();
    void batchProcessReceive();
    
public:
    bool start();
    void stop();
    PerformanceMetrics getMetrics();
};

struct TestClientConfig {
    std::string target_host = "127.0.0.1";
    uint16_t target_port = 8080;
    uint32_t concurrent_connections;               // 并发连接数
    uint32_t connection_ramp_rate = 100;          // 每秒建立连接数
    uint32_t test_duration_seconds = 1800;        // 测试持续时间
    
    // 简化的行为配置
    uint32_t heartbeat_interval_ms = 30000;       // 心跳间隔
    uint32_t data_send_interval_ms = 5000;        // 数据发送间隔
    uint32_t packet_size = 256;                   // 数据包大小
    
    // 性能优化配置
    uint32_t io_thread_count = 2;                 // IO线程数
    uint32_t send_buffer_size = 1024 * 1024;     // 发送缓冲区大小
    uint32_t recv_buffer_size = 1024 * 1024;     // 接收缓冲区大小
    bool use_connection_pool = true;              // 使用连接池
};
```

### 1.2 测试服务器 (Mock Game Server)

#### 核心功能
- **高并发连接处理**：专注于连接管理和数据转发
- **Echo模式**：简单回显数据，最小化处理开销
- **连接状态管理**：维护连接状态，处理超时清理
- **性能监控**：实时监控服务器性能

#### 简化服务器实现
```cpp
class MockGameServer {
private:
    struct ServerConnection {
        uint32_t connection_id;
        boost::asio::ip::tcp::socket socket;
        std::chrono::steady_clock::time_point last_activity;
        std::vector<uint8_t> recv_buffer;
        
        // 最小化状态信息
        bool is_active = true;
    };
    
    boost::asio::io_context io_context_;
    boost::asio::ip::tcp::acceptor acceptor_;
    
    // 使用高效的容器管理连接
    std::unordered_map<uint32_t, std::unique_ptr<ServerConnection>> connections_;
    std::mutex connections_mutex_;
    
    // 简化的处理逻辑
    void handleNewConnection();
    void handleReceive(ServerConnection& conn);
    void handleSend(ServerConnection& conn, const std::vector<uint8_t>& data);
    void cleanupTimeoutConnections();
    
public:
    bool start();
    void stop();
    PerformanceMetrics getMetrics();
};

struct TestServerConfig {
    std::string listen_host = "0.0.0.0";
    uint16_t listen_port = 8080;
    uint32_t max_connections;                     // 最大连接数
    uint32_t io_thread_count = 4;                // IO线程数
    
    // 简化配置
    uint32_t connection_timeout_ms = 300000;     // 连接超时(5分钟)
    uint32_t cleanup_interval_ms = 30000;        // 清理间隔
    bool echo_mode = true;                       // Echo模式
    
    // 性能优化配置
    uint32_t accept_backlog = 1024;              // 监听队列长度
    bool tcp_nodelay = true;                     // 禁用Nagle算法
    uint32_t socket_buffer_size = 64 * 1024;     // Socket缓冲区大小
};
```

### 1.3 性能监控系统

#### 精简的性能指标
```cpp
struct PerformanceMetrics {
    // 核心连接统计
    std::atomic<uint64_t> total_connections_attempted{0};
    std::atomic<uint64_t> total_connections_succeeded{0};
    std::atomic<uint64_t> total_connections_failed{0};
    std::atomic<uint64_t> current_active_connections{0};
    
    // 核心数据统计
    std::atomic<uint64_t> total_bytes_sent{0};
    std::atomic<uint64_t> total_bytes_received{0};
    std::atomic<uint64_t> total_packets_sent{0};
    std::atomic<uint64_t> total_packets_received{0};
    
    // 核心性能统计
    std::atomic<double> current_cps{0.0};        // 连接建立速率
    std::atomic<double> current_throughput_mbps{0.0}; // 吞吐量
    
    // 时间统计
    std::chrono::steady_clock::time_point test_start_time;
    
    // 简化的统计更新
    void updateConnectionStats(bool success);
    void updateDataStats(uint64_t bytes_sent, uint64_t bytes_received);
    void calculateRates();
};

class PerformanceMonitor {
private:
    PerformanceMetrics metrics_;
    std::thread monitor_thread_;
    std::atomic<bool> running_{false};
    
public:
    void start();
    void stop();
    PerformanceMetrics getMetrics() const { return metrics_; }
    
    // 简化的监控逻辑
    void monitorLoop();
    void printStats();
};
```

## 🛠️ 技术选型

### 核心技术栈
- **语言**：C++17
- **网络库**：Boost.Asio 1.80+
- **日志库**：spdlog（最小日志级别）
- **配置库**：nlohmann/json
- **构建系统**：CMake + vcpkg

### 高并发优化策略
- **异步IO**：基于 Boost.Asio 的事件驱动，避免阻塞操作
- **对象池**：预分配连接对象，减少动态内存分配
- **批量操作**：批量发送/接收，减少系统调用次数
- **最小化锁**：使用原子操作和无锁数据结构
- **内存优化**：固定大小缓冲区，避免频繁内存分配

## 🎮 使用场景

### 基础功能测试
```bash
# 启动测试服务器
./mock_server --port=8080 --max-connections=50000 --threads=4

# 启动测试客户端（逐步增加连接数）
./mock_client --host=127.0.0.1 --port=8080 --connections=1000 --ramp-rate=50
./mock_client --host=127.0.0.1 --port=8080 --connections=5000 --ramp-rate=100
./mock_client --host=127.0.0.1 --port=8080 --connections=10000 --ramp-rate=200
```

### 极限压力测试
```bash
# 大并发测试
./mock_client --host=127.0.0.1 --port=8080 --connections=50000 --packet-size=128 --send-interval=10000
```

## 📊 预留接口设计

### 简化的传输层抽象
```cpp
// 最小化的传输接口
class ITransportLayer {
public:
    virtual ~ITransportLayer() = default;
    virtual bool connect(const std::string& host, uint16_t port) = 0;
    virtual bool send(const void* data, size_t size) = 0;  // 直接指针，避免拷贝
    virtual void setReceiveCallback(std::function<void(const void*, size_t)> callback) = 0;
    virtual void disconnect() = 0;
    virtual bool isConnected() const = 0;
};

// 直连实现（高性能）
class DirectTransport : public ITransportLayer {
private:
    boost::asio::ip::tcp::socket socket_;
    std::array<uint8_t, 4096> recv_buffer_;  // 固定大小缓冲区
    
public:
    // 优化的实现
    bool send(const void* data, size_t size) override;
    void startReceive();
};

// 龙盾转发实现（未来）
class DragonShieldTransport : public ITransportLayer {
    // 保持相同的接口，内部通过龙盾转发
};
```

## 📝 总结

**简化设计原则**：
1. **最小化协议开销** - 简单的包头结构，固定大小数据包
2. **减少状态管理** - 只维护必要的连接状态信息
3. **优化内存使用** - 对象池、固定缓冲区、减少动态分配
4. **简化业务逻辑** - Echo模式，避免复杂的游戏逻辑处理
5. **批量操作** - 减少系统调用，提高IO效率

**性能优先目标**：
1. 支持大量并发连接（目标：几万连接）
2. 低CPU和内存占用
3. 高网络吞吐量
4. 稳定的长时间运行

**实现优先级**：
1. 实现基础TCP连接管理（Echo模式）
2. 添加简单的心跳和数据发送
3. 优化高并发性能
4. 完善监控和统计系统

这样的设计专注于压力测试的核心目标，避免了复杂的游戏逻辑，确保能够实现真正的高并发测试。
