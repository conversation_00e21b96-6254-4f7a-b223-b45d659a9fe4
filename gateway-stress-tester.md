# 网关压力测试工具开发项目

## 📋 项目概述

基于需求文档 `gateway-load-tester/需求文档.md` 的设计思路，开发轻量级的测试客户端和测试服务器，专注于高并发连接和数据传输测试。

### 核心目标
- **第一阶段**：实现直连模式的高并发压力测试（Mock Game Client → Mock Game Server）
- **第二阶段**：预留接口支持龙盾转发模式（测试客户端 → 龙盾客户端 → 网关 → 测试服务器）

## 🏗️ 项目结构

```
gateway-stress-tester/
├── CMakeLists.txt                 # 主构建文件
├── README.md                      # 项目说明
├── config/                        # 配置文件
│   ├── client_config.json         # 客户端配置
│   └── server_config.json         # 服务器配置
├── include/                       # 头文件
│   ├── transport/                 # 传输层抽象
│   │   ├── transport_interface.h  # 传输接口定义
│   │   └── direct_transport.h     # 直连传输实现
│   ├── client/                    # 客户端相关
│   │   ├── mock_game_client.h     # 模拟游戏客户端
│   │   └── client_connection.h    # 客户端连接管理
│   ├── server/                    # 服务器相关
│   │   ├── mock_game_server.h     # 模拟游戏服务器
│   │   └── server_connection.h    # 服务器连接管理
│   ├── protocol/                  # 协议定义
│   │   └── simple_protocol.h      # 简化协议定义
│   ├── monitoring/                # 性能监控
│   │   └── performance_monitor.h  # 性能监控器
│   └── utils/                     # 工具类
│       ├── connection_pool.h      # 连接池
│       └── ring_buffer.h          # 环形缓冲区
├── src/                           # 源文件
│   ├── transport/
│   │   └── direct_transport.cpp
│   ├── client/
│   │   ├── mock_game_client.cpp
│   │   └── client_connection.cpp
│   ├── server/
│   │   ├── mock_game_server.cpp
│   │   └── server_connection.cpp
│   ├── monitoring/
│   │   └── performance_monitor.cpp
│   ├── utils/
│   │   ├── connection_pool.cpp
│   │   └── ring_buffer.cpp
│   ├── client_main.cpp            # 客户端入口
│   └── server_main.cpp            # 服务器入口
├── tests/                         # 测试文件
│   ├── test_transport.cpp
│   ├── test_client.cpp
│   └── test_server.cpp
└── docs/                          # 文档
    ├── design_principles.md       # 设计原则
    ├── performance_optimization.md # 性能优化指南
    └── usage_guide.md             # 使用指南
```

## 🎯 开发原则

### 1. 遵循编码规则
- 严格遵循 `.cursor/rules/allrule.mdc` 和 `.cursor/rules/edit.mdc`
- 避免头文件中的复杂内联函数
- 接口在头文件，实现在源文件
- 使用 UTF-8 编码

### 2. 增量开发
- 先实现最小可编译单元
- 每添加一个文件就测试编译
- 优先简化版本，确保编译通过后再添加完整功能

### 3. 性能优先
- 专注于压测网关，不是开发完整的游戏客户端
- 保持简化协议（16字节 vs 88字节）
- Echo模式服务器，避免业务逻辑干扰
- 高并发优化：对象池、批量操作、固定缓冲区

## 🛠️ 技术选型

- **语言**：C++17
- **网络库**：Boost.Asio 1.80+
- **构建系统**：CMake (参考 mini_booster 方式)
- **依赖管理**：vcpkg
- **编译器**：Visual Studio 2022

## 📊 核心设计

### 简化协议
```cpp
struct PacketHeader {
    uint32_t packet_type;    // 包类型
    uint32_t packet_length;  // 包长度
    uint32_t connection_id;  // 连接ID
    uint32_t sequence_num;   // 序列号
}; // 仅16字节，专注压测效率
```

### 传输层抽象
```cpp
class ITransportLayer {
public:
    virtual bool connect(const std::string& host, uint16_t port) = 0;
    virtual bool send(const void* data, size_t size) = 0;
    virtual void disconnect() = 0;
    virtual bool isConnected() const = 0;
};
```

## 📈 预期性能目标

- **并发连接数**：几万连接
- **连接建立速率**：每秒数百连接
- **CPU使用率**：< 80%（单机）
- **内存使用**：< 4GB（单机）
- **网络延迟**：< 1ms（本地测试）

## 🚀 开发计划

1. **阶段1**：建立基础项目结构和CMake配置
2. **阶段2**：实现简化协议和传输接口
3. **阶段3**：开发Mock客户端和服务器
4. **阶段4**：添加性能监控和统计
5. **阶段5**：高并发优化和压力测试
6. **阶段6**：预留龙盾集成接口

---

> 本项目严格按照需求文档的设计思路实施，确保压测工具的高效性和可扩展性。