cmake_minimum_required(VERSION 3.15)
project(stress-test VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# MSVC compiler settings for UTF-8 encoding support
if(MSVC)
    add_compile_options("/utf-8")
    
    # Disable overly strict static analysis warnings
    add_compile_options("/wd4996")  # Disable deprecated function warnings
    add_compile_options("/wd6385")  # Disable invalid data read warnings
    add_compile_options("/wd6386")  # Disable buffer overflow warnings
    add_compile_options("/wd28182") # Disable SAL annotation warnings
    add_compile_options("/wd26451") # Disable arithmetic overflow warnings
    add_compile_options("/wd26495") # Disable uninitialized member variable warnings
    add_compile_options("/wd26812") # Disable enum class warnings
    add_compile_options("/wd4244")  # Disable type conversion warnings
    add_compile_options("/wd4267")  # Disable size_t conversion warnings
    
    # Release mode optimization settings
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options("/O2")
        add_compile_options("/GL")
        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded")
    else()
        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreadedDLL")
    endif()
endif()

# Find required packages
find_package(Boost REQUIRED COMPONENTS system)
find_package(Threads REQUIRED)

# Include directories
include_directories(include)

# Create directories for source files
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/transport)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/client)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/server)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/monitoring)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/utils)

# Mock Game Client executable
add_executable(mock-client
    src/client_main.cpp
    src/transport/direct_transport.cpp
    src/client/mock_game_client.cpp
    src/client/client_connection.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

target_link_libraries(mock-client
    Boost::system
    Threads::Threads
)

# Mock Game Server executable
add_executable(mock-server
    src/server_main.cpp
    src/transport/direct_transport.cpp
    src/server/mock_game_server.cpp
    src/server/server_connection.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

target_link_libraries(mock-server
    Boost::system
    Threads::Threads
)

# Test executables
add_executable(test-transport
    tests/test_transport.cpp
    src/transport/direct_transport.cpp
)

target_link_libraries(test-transport
    Boost::system
    Threads::Threads
)

add_executable(test-client
    tests/test_client.cpp 
    src/client/mock_game_client.cpp
    src/client/client_connection.cpp
    src/transport/direct_transport.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

target_link_libraries(test-client
    Boost::system
    Threads::Threads
)

add_executable(test-server
    tests/test_server.cpp
    src/server/mock_game_server.cpp
    src/server/server_connection.cpp
    src/transport/direct_transport.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

target_link_libraries(test-server
    Boost::system
    Threads::Threads
)