cmake_minimum_required(VERSION 3.15)
project(stress-test VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# MSVC compiler settings (matching mini_booster)
if(MSVC)
    add_compile_options(
        /W3                    # Use warning level 3 instead of 4
        /wd4996               # Suppress deprecated function warnings
        /wd6001               # Suppress uninitialized memory warnings
        /wd6385               # Suppress buffer overflow warnings
        /wd6386               # Suppress buffer overflow warnings
        /wd28182              # Suppress SAL annotation warnings
        /wd28183              # Suppress SAL annotation warnings
        /wd26495              # Suppress uninitialized member variable warnings
        /wd26812              # Suppress enum class preference warnings
        /wd4244               # Suppress type conversion warnings
        /wd4267               # Suppress size_t conversion warnings
        /wd26818              # Suppress switch fallthrough warnings
        /utf-8                # Force UTF-8 encoding
    )
    
    # Suppress specific code analysis warnings
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(_SCL_SECURE_NO_WARNINGS)
    
    # Debug/Release mode settings
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(/O2 /GL)
        add_link_options(/LTCG)
        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded")
    else()
        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreadedDebug")
    endif()
endif()

# Output directory configuration
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Find Boost library (managed through vcpkg)
find_package(Boost REQUIRED)

# Mock Game Server executable
add_executable(mock-server
    src/server_main.cpp
    src/transport/direct_transport.cpp
    src/server/mock_game_server.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

# Add header file directories
target_include_directories(mock-server PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

# Link libraries
target_link_libraries(mock-server PRIVATE Boost::boost)

# Mock Game Client executable  
add_executable(mock-client
    src/client_main.cpp
    src/transport/direct_transport.cpp
    src/client/mock_game_client.cpp
    src/monitoring/performance_monitor.cpp
    src/utils/connection_pool.cpp
    src/utils/ring_buffer.cpp
)

target_include_directories(mock-client PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

target_link_libraries(mock-client PRIVATE Boost::boost)

# Test executables
add_executable(test-basic
    tests/test_basic.cpp
    src/monitoring/performance_monitor.cpp
)

target_include_directories(test-basic PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

target_link_libraries(test-basic PRIVATE Boost::boost)

add_executable(test-transport
    tests/test_transport.cpp
    src/transport/direct_transport.cpp
)

target_include_directories(test-transport PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

target_link_libraries(test-transport PRIVATE Boost::boost)

# Windows platform compile definitions
if(WIN32)
    target_compile_definitions(mock-server PRIVATE _WIN32_WINNT=0x0A00)
    target_compile_definitions(mock-client PRIVATE _WIN32_WINNT=0x0A00)
    target_compile_definitions(test-basic PRIVATE _WIN32_WINNT=0x0A00)
    target_compile_definitions(test-transport PRIVATE _WIN32_WINNT=0x0A00)
endif()

# Install rules
install(TARGETS mock-server mock-client test-basic test-transport
    RUNTIME DESTINATION bin
)