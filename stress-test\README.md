# 网关压力测试工具

## 项目简介

轻量级的高并发网络压力测试工具，专注于测试网关的极限连接数和性能表现。

### 核心特性

- **简化协议**：16字节PacketHeader，专注压测效率
- **高并发优化**：对象池、批量操作、固定缓冲区
- **Echo模式**：最小化业务逻辑，突出网络性能
- **分阶段设计**：当前直连模式，未来支持龙盾转发

## 快速开始

### 构建项目

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### 运行测试

```bash
# 启动测试服务器
.\mock-server.exe --port=8080 --max-connections=10000

# 启动测试客户端
.\mock-client.exe --host=127.0.0.1 --port=8080 --connections=1000
```

## 性能目标

- **并发连接数**：10,000+ 连接
- **连接建立速率**：500+ CPS
- **CPU使用率**：< 80%
- **内存使用**：< 2GB

## 技术栈

- C++17
- Boost.Asio
- CMake
- Visual Studio 2022