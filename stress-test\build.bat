@echo off
echo Building stress-test project...

if not exist build mkdir build
cd build

echo Configuring CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo Building project...
cmake --build . --config Release

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo Executables are in: build\Release\
echo - mock-server.exe
echo - mock-client.exe
echo - test-transport.exe
echo.
pause