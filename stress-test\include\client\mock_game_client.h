#pragma once

#include "transport/transport_interface.h"
#include <boost/asio.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <memory>
#include <vector>
#include <atomic>

namespace stress_test {

namespace monitoring {
class PerformanceMonitor;
}

namespace client {

// Simple client connection for testing
class ClientConnection {
public:
    enum class State {
        Disconnected,
        Connected,
        Active
    };
    
    explicit ClientConnection(boost::asio::io_context& io_context);
    ~ClientConnection();
    
    void connect(const std::string& host, uint16_t port, std::function<void(bool)> callback);
    void disconnect();
    bool isConnected() const;
    
    void startHeartbeat();
    void stopHeartbeat();
    void sendTestData();

private:
    void onDataReceived(const void* data, size_t size);
    void sendHeartbeatPacket();

private:
    boost::asio::io_context& io_context_;
    std::unique_ptr<transport::ITransportLayer> transport_;
    boost::asio::deadline_timer heartbeat_timer_;
    
    State state_;
    uint32_t connection_id_;
    uint32_t sequence_num_;
    
    static std::atomic<uint32_t> next_connection_id_;
};

// Mock game client for pressure testing
class MockGameClient {
public:
    explicit MockGameClient(boost::asio::io_context& io_context);
    ~MockGameClient();
    
    bool createConnections(const std::string& host, uint16_t port, uint32_t count);
    void startHeartbeat();
    void startDataSend();
    void stop();
    
    void setPerformanceMonitor(monitoring::PerformanceMonitor* monitor);
    uint32_t getActiveConnections() const;

private:
    boost::asio::io_context& io_context_;
    std::vector<std::unique_ptr<ClientConnection>> connections_;
    monitoring::PerformanceMonitor* performance_monitor_;
    std::atomic<bool> running_;
};

} // namespace client
} // namespace stress_test