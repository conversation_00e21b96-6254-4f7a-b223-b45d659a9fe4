#pragma once

#include <atomic>
#include <chrono>
#include <thread>

namespace stress_test {
namespace monitoring {

// Simple performance metrics for pressure testing
struct PerformanceMetrics {
    // Connection statistics
    std::atomic<uint64_t> total_connections_attempted{0};
    std::atomic<uint64_t> total_connections_succeeded{0};
    std::atomic<uint64_t> total_connections_failed{0};
    std::atomic<uint64_t> current_active_connections{0};
    
    // Data transfer statistics
    std::atomic<uint64_t> total_bytes_sent{0};
    std::atomic<uint64_t> total_bytes_received{0};
    std::atomic<uint64_t> total_packets_sent{0};
    std::atomic<uint64_t> total_packets_received{0};
    
    // Performance rates
    std::atomic<double> current_cps{0.0};          // Connections per second
    std::atomic<double> current_throughput_mbps{0.0}; // Throughput in Mbps
    
    // Test timing
    std::chrono::steady_clock::time_point test_start_time;
};

// Performance monitor for real-time statistics
class PerformanceMonitor {
public:
    PerformanceMonitor();
    ~PerformanceMonitor();
    
    void start();
    void stop();
    
    PerformanceMetrics& getMetrics();
    const PerformanceMetrics& getMetrics() const;
    
    void updateConnectionStats(bool success);
    void updateDataStats(uint64_t bytes_sent, uint64_t bytes_received);
    void updatePacketStats(uint64_t packets_sent, uint64_t packets_received);

private:
    void monitorLoop();
    void calculateRates();
    void printStats();

private:
    PerformanceMetrics metrics_;
    std::thread monitor_thread_;
    std::atomic<bool> running_;
    
    // For rate calculations
    std::chrono::steady_clock::time_point last_calculation_time_;
    uint64_t last_connections_succeeded_;
    uint64_t last_bytes_total_;
};

} // namespace monitoring
} // namespace stress_test