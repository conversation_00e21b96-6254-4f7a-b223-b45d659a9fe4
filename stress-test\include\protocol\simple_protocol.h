#pragma once

#include <cstdint>

namespace stress_test {
namespace protocol {

// Packet types for simple protocol
constexpr uint32_t PACKET_TYPE_HEARTBEAT = 1;
constexpr uint32_t PACKET_TYPE_DATA = 2;
constexpr uint32_t PACKET_TYPE_ECHO_REQUEST = 3;
constexpr uint32_t PACKET_TYPE_ECHO_RESPONSE = 4;

#pragma pack(push, 1)

// Simple 16-byte packet header for high performance testing
struct PacketHeader {
    uint32_t packet_type;    // Packet type identifier
    uint32_t packet_length;  // Total packet length including header
    uint32_t connection_id;  // Connection identifier
    uint32_t sequence_num;   // Packet sequence number
};

// Heartbeat packet (16 bytes total)
struct HeartbeatPacket {
    PacketHeader header;
    uint32_t timestamp;      // Timestamp for latency calculation
    uint32_t reserved;       // Reserved for future use
};

// Simple data packet (variable length)
struct DataPacket {
    PacketHeader header;
    // Followed by data payload
};

#pragma pack(pop)

// Protocol constants
constexpr size_t MAX_PACKET_SIZE = 65536;
constexpr size_t HEADER_SIZE = sizeof(PacketHeader);
constexpr size_t HEARTBEAT_SIZE = sizeof(HeartbeatPacket);
constexpr uint32_t DEFAULT_HEARTBEAT_INTERVAL_MS = 30000;
constexpr uint32_t DEFAULT_DATA_SEND_INTERVAL_MS = 5000;

} // namespace protocol
} // namespace stress_test