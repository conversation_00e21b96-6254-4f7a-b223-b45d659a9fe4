#pragma once

#include <boost/asio.hpp>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <atomic>

namespace stress_test {

namespace monitoring {
class PerformanceMonitor;
}

namespace server {

// Simple connection handler for server side
class ServerConnection : public std::enable_shared_from_this<ServerConnection> {
public:
    explicit ServerConnection(boost::asio::ip::tcp::socket socket);
    ~ServerConnection();
    
    void start();
    void stop();
    uint32_t getId() const { return connection_id_; }

private:
    void startRead();
    void handleRead(const boost::system::error_code& error, size_t bytes_transferred);
    void handleWrite(const boost::system::error_code& error, size_t bytes_transferred);
    void echoData(const void* data, size_t size);

private:
    boost::asio::ip::tcp::socket socket_;
    uint32_t connection_id_;
    bool active_;
    std::chrono::steady_clock::time_point last_activity_;
    
    static constexpr size_t BUFFER_SIZE = 4096;
    std::array<uint8_t, BUFFER_SIZE> read_buffer_;
    
    static std::atomic<uint32_t> next_connection_id_;
};

// Mock game server for echo mode testing
class MockGameServer {
public:
    explicit MockGameServer(boost::asio::io_context& io_context);
    ~MockGameServer();
    
    bool start(const std::string& host, uint16_t port, uint32_t max_connections);
    void stop();
    
    void setPerformanceMonitor(monitoring::PerformanceMonitor* monitor);
    uint32_t getActiveConnections() const;

private:
    void startAccept();
    void handleAccept(const boost::system::error_code& error);
    void cleanupConnections();

private:
    boost::asio::io_context& io_context_;
    std::unique_ptr<boost::asio::ip::tcp::acceptor> acceptor_;
    
    std::unordered_map<uint32_t, std::shared_ptr<ServerConnection>> connections_;
    mutable std::mutex connections_mutex_;
    
    uint32_t max_connections_;
    std::atomic<bool> running_;
    
    monitoring::PerformanceMonitor* performance_monitor_;
    std::shared_ptr<ServerConnection> pending_connection_;
};

} // namespace server
} // namespace stress_test