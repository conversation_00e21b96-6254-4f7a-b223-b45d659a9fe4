#pragma once

#include "transport_interface.h"
#include <boost/asio.hpp>
#include <memory>
#include <array>

namespace stress_test {
namespace transport {

// Direct TCP transport implementation for high performance testing
class DirectTransport : public ITransportLayer {
public:
    explicit DirectTransport(boost::asio::io_context& io_context);
    virtual ~DirectTransport();
    
    // ITransportLayer interface
    bool connect(const std::string& host, uint16_t port) override;
    bool send(const void* data, size_t size) override;
    void setReceiveCallback(ReceiveCallback callback) override;
    void disconnect() override;
    bool isConnected() const override;

private:
    void startReceive();
    void handleConnect(const boost::system::error_code& error);
    void handleSend(const boost::system::error_code& error, size_t bytes_transferred);
    void handleReceive(const boost::system::error_code& error, size_t bytes_transferred);

private:
    boost::asio::io_context& io_context_;
    boost::asio::ip::tcp::socket socket_;
    ReceiveCallback receive_callback_;
    bool connected_;
    
    // Fixed buffer for high performance
    static constexpr size_t RECEIVE_BUFFER_SIZE = 4096;
    std::array<uint8_t, RECEIVE_BUFFER_SIZE> receive_buffer_;
};

} // namespace transport
} // namespace stress_test