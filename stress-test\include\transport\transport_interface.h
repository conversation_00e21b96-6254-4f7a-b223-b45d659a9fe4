#pragma once

#include <string>
#include <functional>
#include <cstddef>

namespace stress_test {
namespace transport {

// Simple transport layer interface for pressure testing
class ITransportLayer {
public:
    using ReceiveCallback = std::function<void(const void* data, size_t size)>;
    
    virtual ~ITransportLayer() = default;
    
    // Connect to target host and port
    virtual bool connect(const std::string& host, uint16_t port) = 0;
    
    // Send data directly without copying
    virtual bool send(const void* data, size_t size) = 0;
    
    // Set callback for received data
    virtual void setReceiveCallback(ReceiveCallback callback) = 0;
    
    // Disconnect from target
    virtual void disconnect() = 0;
    
    // Check connection status
    virtual bool isConnected() const = 0;
};

} // namespace transport
} // namespace stress_test