#pragma once

#include <memory>
#include <queue>
#include <mutex>

namespace stress_test {
namespace utils {

// Simple connection pool for reusing connection objects
template<typename T>
class ConnectionPool {
public:
    ConnectionPool() = default;
    ~ConnectionPool() = default;
    
    // Get connection from pool or create new one
    std::unique_ptr<T> acquire();
    
    // Return connection to pool
    void release(std::unique_ptr<T> obj);
    
    // Get current pool size
    size_t size() const;

private:
    mutable std::mutex mutex_;
    std::queue<std::unique_ptr<T>> pool_;
};

// Template implementation
template<typename T>
std::unique_ptr<T> ConnectionPool<T>::acquire() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!pool_.empty()) {
        auto obj = std::move(pool_.front());
        pool_.pop();
        return obj;
    }
    
    return std::make_unique<T>();
}

template<typename T>
void ConnectionPool<T>::release(std::unique_ptr<T> obj) {
    if (obj) {
        std::lock_guard<std::mutex> lock(mutex_);
        pool_.push(std::move(obj));
    }
}

template<typename T>
size_t ConnectionPool<T>::size() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return pool_.size();
}

} // namespace utils
} // namespace stress_test