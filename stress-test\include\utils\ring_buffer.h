#pragma once

#include <vector>
#include <atomic>

namespace stress_test {
namespace utils {

// Simple ring buffer for high performance I/O
class RingBuffer {
public:
    explicit RingBuffer(size_t capacity);
    ~RingBuffer() = default;
    
    // Write data to buffer, returns bytes written
    size_t write(const void* data, size_t size);
    
    // Read data from buffer, returns bytes read  
    size_t read(void* data, size_t size);
    
    // Get available space for writing
    size_t writeAvailable() const;
    
    // Get available data for reading
    size_t readAvailable() const;
    
    // Check if buffer is empty
    bool empty() const;
    
    // Check if buffer is full
    bool full() const;
    
    // Clear buffer
    void clear();

private:
    std::vector<uint8_t> buffer_;
    std::atomic<size_t> read_pos_;
    std::atomic<size_t> write_pos_;
    size_t capacity_;
};

} // namespace utils
} // namespace stress_test