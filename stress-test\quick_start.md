# 快速开始指南

## 构建项目

### 方法1：使用批处理脚本
```cmd
build.bat
```

### 方法2：手动构建
```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

## 运行测试

### 1. 启动测试服务器
```cmd
cd build\Release
mock-server.exe --port=8080 --max-connections=10000 --threads=4
```

### 2. 启动测试客户端
```cmd
cd build\Release
mock-client.exe --host=127.0.0.1 --port=8080 --connections=1000 --duration=60
```

## 配置说明

### 服务器参数
- `--host=<host>`: 监听地址（默认：0.0.0.0）
- `--port=<port>`: 监听端口（默认：8080）
- `--max-connections=<n>`: 最大连接数（默认：10000）
- `--threads=<n>`: IO线程数（默认：CPU核心数）

### 客户端参数
- `--host=<host>`: 目标地址（默认：127.0.0.1）
- `--port=<port>`: 目标端口（默认：8080）
- `--connections=<n>`: 连接数（默认：1000）
- `--threads=<n>`: IO线程数（默认：CPU核心数）
- `--duration=<n>`: 测试时长秒数（默认：60）

## 测试场景

### 基础连接测试
```cmd
# 服务器
mock-server.exe --port=8080 --max-connections=1000

# 客户端
mock-client.exe --connections=100 --duration=30
```

### 高并发压力测试
```cmd
# 服务器
mock-server.exe --port=8080 --max-connections=50000 --threads=8

# 客户端
mock-client.exe --connections=10000 --duration=300 --threads=4
```

## 性能监控

程序运行时会实时显示：
- 当前活跃连接数
- 每秒连接建立速率（CPS）
- 网络吞吐量（Mbps）
- 发送/接收包数量

## 故障排除

1. **编译错误**：确保已安装 Visual Studio 2022 和 vcpkg
2. **连接失败**：检查端口是否被占用，防火墙设置
3. **性能不达预期**：调整线程数，检查系统资源限制