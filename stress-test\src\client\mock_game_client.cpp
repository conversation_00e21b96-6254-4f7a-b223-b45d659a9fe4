#include "client/mock_game_client.h"
#include "transport/direct_transport.h"
#include "protocol/simple_protocol.h"
#include "monitoring/performance_monitor.h"
#include <iostream>
#include <string>
#include <functional>
#include <atomic>
#include <thread>
#include <chrono>

namespace stress_test {
namespace client {

// ClientConnection implementation
std::atomic<uint32_t> ClientConnection::next_connection_id_(1);

ClientConnection::ClientConnection(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , heartbeat_timer_(io_context)
    , state_(State::Disconnected)
    , connection_id_(next_connection_id_.fetch_add(1))
    , sequence_num_(0)
    , retry_count_(0)
    , performance_monitor_(nullptr) {
    
    transport_ = std::make_shared<transport::DirectTransport>(io_context);
}

ClientConnection::~ClientConnection() {
    disconnect();
}

void ClientConnection::connect(const std::string& host, uint16_t port, std::function<void(bool)> callback) {
    if (state_ != State::Disconnected) {
        std::cout << "Connection " << connection_id_ << ": Already connected or connecting" << std::endl;
        callback(false);
        return;
    }
    
    std::cout << "Connection " << connection_id_ << ": Attempting to connect to " << host << ":" << port << std::endl;
    
    connect_callback_ = callback;
    state_ = State::Connecting;
    
    // Set up transport connection callback
    transport_->setReceiveCallback(
        std::bind(&ClientConnection::onDataReceived, this, 
                  std::placeholders::_1, std::placeholders::_2));
    
    if (transport_->connect(host, port)) {
        std::cout << "Connection " << connection_id_ << ": Transport connect initiated" << std::endl;
        // Connection initiated successfully
        // Wait for actual connection in transport layer
        startConnectionCheck();
    } else {
        std::cout << "Connection " << connection_id_ << ": Transport connect failed" << std::endl;
        state_ = State::Disconnected;
        callback(false);
    }
}

void ClientConnection::disconnect() {
    state_ = State::Disconnected;
    stopHeartbeat();
    heartbeat_timer_.cancel();
    
    if (transport_) {
        transport_->disconnect();
    }
}

bool ClientConnection::isConnected() const {
    return (state_ == State::Connected || state_ == State::Active) && transport_ && transport_->isConnected();
}

void ClientConnection::startConnectionCheck() {
    // Check connection status after a short delay
    heartbeat_timer_.expires_from_now(boost::posix_time::milliseconds(100));
    heartbeat_timer_.async_wait(
        [this](const boost::system::error_code& ec) {
            if (!ec) {
                this->checkConnectionStatus();
            }
        });
}

void ClientConnection::checkConnectionStatus() {
    if (state_ == State::Connecting) {
        bool transport_exists = (transport_ != nullptr);
        bool is_connected = transport_exists && transport_->isConnected();
        
        if (retry_count_ == 1) { // Log debug info on first check
            std::cout << "Connection " << connection_id_ << ": Checking status - transport exists: " 
                      << (transport_exists ? "yes" : "no") 
                      << ", connected: " << (is_connected ? "yes" : "no") << std::endl;
        }
        
        if (is_connected) {
            std::cout << "Connection " << connection_id_ << ": Successfully connected!" << std::endl;
            state_ = State::Connected;
            retry_count_ = 0;
            if (connect_callback_) {
                connect_callback_(true);
            }
        } else {
            // Try again after a short delay, with timeout
            if (retry_count_ < 100) { // 10 seconds timeout (100 * 100ms)
                retry_count_++;
                if (retry_count_ % 20 == 0) { // Log every 2 seconds
                    std::cout << "Connection " << connection_id_ << ": Still connecting... retry " << retry_count_ << "/100" << std::endl;
                }
                startConnectionCheck();
            } else {
                std::cout << "Connection " << connection_id_ << ": Connection timeout after 10 seconds" << std::endl;
                retry_count_ = 0;
                state_ = State::Disconnected;
                if (connect_callback_) {
                    connect_callback_(false);
                }
            }
        }
    }
}

void ClientConnection::startHeartbeat() {
    if (state_ == State::Connected) {
        state_ = State::Active;
        sendHeartbeatPacket();
    }
}

void ClientConnection::stopHeartbeat() {
    heartbeat_timer_.cancel();
}

void ClientConnection::sendTestData() {
    if (!isConnected()) {
        return;
    }
    
    protocol::DataPacket packet;
    packet.header.packet_type = protocol::PACKET_TYPE_ECHO_REQUEST;
    packet.header.packet_length = sizeof(protocol::PacketHeader);
    packet.header.connection_id = connection_id_;
    packet.header.sequence_num = ++sequence_num_;
    
    if (transport_->send(&packet, sizeof(protocol::PacketHeader))) {
        if (performance_monitor_) {
            performance_monitor_->updateDataStats(sizeof(protocol::PacketHeader), 0);
            performance_monitor_->updatePacketStats(1, 0);
        }
    }
}

void ClientConnection::setPerformanceMonitor(monitoring::PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
}

void ClientConnection::onDataReceived(const void* data, size_t size) {
    if (performance_monitor_) {
        performance_monitor_->updateDataStats(0, size);
        performance_monitor_->updatePacketStats(0, 1);
    }
    
    // Simple echo response handling
    if (size >= sizeof(protocol::PacketHeader)) {
        const protocol::PacketHeader* header = 
            static_cast<const protocol::PacketHeader*>(data);
        
        if (header->packet_type == protocol::PACKET_TYPE_ECHO_RESPONSE) {
            // Echo received successfully
        }
    }
}

void ClientConnection::sendHeartbeatPacket() {
    if (!isConnected()) {
        return;
    }
    
    protocol::HeartbeatPacket packet;
    packet.header.packet_type = protocol::PACKET_TYPE_HEARTBEAT;
    packet.header.packet_length = sizeof(protocol::HeartbeatPacket);
    packet.header.connection_id = connection_id_;
    packet.header.sequence_num = ++sequence_num_;
    packet.timestamp = static_cast<uint32_t>(
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count());
    packet.reserved = 0;
    
    if (transport_->send(&packet, sizeof(protocol::HeartbeatPacket))) {
        if (performance_monitor_) {
            performance_monitor_->updateDataStats(sizeof(protocol::HeartbeatPacket), 0);
            performance_monitor_->updatePacketStats(1, 0);
        }
    }
    
    // Schedule next heartbeat
    heartbeat_timer_.expires_from_now(
        boost::posix_time::milliseconds(protocol::DEFAULT_HEARTBEAT_INTERVAL_MS));
    heartbeat_timer_.async_wait(
        [this](const boost::system::error_code& ec) {
            if (!ec) {
                this->sendHeartbeatPacket();
            }
        });
}

// MockGameClient implementation
MockGameClient::MockGameClient(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , performance_monitor_(nullptr)
    , running_(false) {
}

MockGameClient::~MockGameClient() {
    stop();
}

bool MockGameClient::createConnections(const std::string& host, uint16_t port, uint32_t count) {
    connections_.reserve(count);
    
    std::atomic<uint32_t> successful{0};
    std::atomic<uint32_t> completed{0};
    
    for (uint32_t i = 0; i < count; ++i) {
        auto connection = std::make_unique<ClientConnection>(io_context_);
        auto connection_ptr = connection.get();
        connections_.push_back(std::move(connection));
        
        connection_ptr->connect(host, port, [this, &successful, &completed, count](bool success) {
            if (success) {
                successful.fetch_add(1);
                if (performance_monitor_) {
                    performance_monitor_->updateConnectionStats(true);
                }
            } else {
                if (performance_monitor_) {
                    performance_monitor_->updateConnectionStats(false);
                }
            }
            completed.fetch_add(1);
        });
    }
    
    // Wait for all connections to complete (with timeout)
    auto start_time = std::chrono::steady_clock::now();
    while (completed.load() < count) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        // 10 second timeout
        if (std::chrono::steady_clock::now() - start_time > std::chrono::seconds(10)) {
            break;
        }
    }
    
    uint32_t final_successful = successful.load();
    std::cout << "Created " << final_successful << "/" << count << " connections" << std::endl;
    return final_successful > 0;
}

void MockGameClient::startHeartbeat() {
    running_ = true;
    
    for (auto& connection : connections_) {
        connection->setPerformanceMonitor(performance_monitor_);
        if (connection->isConnected()) {
            connection->startHeartbeat();
        }
    }
}

void MockGameClient::startDataSend() {
    // For pressure testing, we can periodically send test data
    for (auto& connection : connections_) {
        connection->setPerformanceMonitor(performance_monitor_);
        if (connection->isConnected()) {
            connection->sendTestData();
        }
    }
}

void MockGameClient::stop() {
    running_ = false;
    
    for (auto& connection : connections_) {
        connection->disconnect();
    }
    
    connections_.clear();
}

void MockGameClient::setPerformanceMonitor(monitoring::PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
}

uint32_t MockGameClient::getActiveConnections() const {
    uint32_t active = 0;
    for (const auto& connection : connections_) {
        if (connection->isConnected()) {
            active++;
        }
    }
    return active;
}

} // namespace client
} // namespace stress_test