#include "client/mock_game_client.h"
#include "transport/direct_transport.h"
#include "protocol/simple_protocol.h"
#include "monitoring/performance_monitor.h"
#include <iostream>
#include <string>
#include <functional>

namespace stress_test {
namespace client {

// ClientConnection implementation
std::atomic<uint32_t> ClientConnection::next_connection_id_(1);

ClientConnection::ClientConnection(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , heartbeat_timer_(io_context)
    , state_(State::Disconnected)
    , connection_id_(next_connection_id_.fetch_add(1))
    , sequence_num_(0) {
    
    transport_ = std::make_unique<transport::DirectTransport>(io_context);
    transport_->setReceiveCallback(
        std::bind(&ClientConnection::onDataReceived, this, 
                  std::placeholders::_1, std::placeholders::_2));
}

ClientConnection::~ClientConnection() {
    disconnect();
}

bool ClientConnection::connect(const std::string& host, uint16_t port) {
    if (state_ != State::Disconnected) {
        return false;
    }
    
    if (transport_->connect(host, port)) {
        state_ = State::Connected;
        return true;
    }
    
    return false;
}

void ClientConnection::disconnect() {
    state_ = State::Disconnected;
    stopHeartbeat();
    
    if (transport_) {
        transport_->disconnect();
    }
}

bool ClientConnection::isConnected() const {
    return state_ != State::Disconnected && transport_ && transport_->isConnected();
}

void ClientConnection::startHeartbeat() {
    if (state_ == State::Connected) {
        state_ = State::Active;
        sendHeartbeatPacket();
    }
}

void ClientConnection::stopHeartbeat() {
    heartbeat_timer_.cancel();
}

void ClientConnection::sendTestData() {
    if (!isConnected()) {
        return;
    }
    
    protocol::DataPacket packet;
    packet.header.packet_type = protocol::PACKET_TYPE_ECHO_REQUEST;
    packet.header.packet_length = sizeof(protocol::PacketHeader);
    packet.header.connection_id = connection_id_;
    packet.header.sequence_num = ++sequence_num_;
    
    transport_->send(&packet, sizeof(protocol::PacketHeader));
}

void ClientConnection::onDataReceived(const void* data, size_t size) {
    // Simple echo response handling
    if (size >= sizeof(protocol::PacketHeader)) {
        const protocol::PacketHeader* header = 
            static_cast<const protocol::PacketHeader*>(data);
        
        if (header->packet_type == protocol::PACKET_TYPE_ECHO_RESPONSE) {
            // Echo received successfully
        }
    }
}

void ClientConnection::sendHeartbeatPacket() {
    if (!isConnected()) {
        return;
    }
    
    protocol::HeartbeatPacket packet;
    packet.header.packet_type = protocol::PACKET_TYPE_HEARTBEAT;
    packet.header.packet_length = sizeof(protocol::HeartbeatPacket);
    packet.header.connection_id = connection_id_;
    packet.header.sequence_num = ++sequence_num_;
    packet.timestamp = static_cast<uint32_t>(
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count());
    packet.reserved = 0;
    
    transport_->send(&packet, sizeof(protocol::HeartbeatPacket));
    
    // Schedule next heartbeat
    heartbeat_timer_.expires_from_now(
        boost::posix_time::milliseconds(protocol::DEFAULT_HEARTBEAT_INTERVAL_MS));
    heartbeat_timer_.async_wait(
        [this](const boost::system::error_code& ec) {
            if (!ec) {
                this->sendHeartbeatPacket();
            }
        });
}

// MockGameClient implementation
MockGameClient::MockGameClient(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , performance_monitor_(nullptr)
    , running_(false) {
}

MockGameClient::~MockGameClient() {
    stop();
}

bool MockGameClient::createConnections(const std::string& host, uint16_t port, uint32_t count) {
    connections_.reserve(count);
    
    uint32_t successful = 0;
    for (uint32_t i = 0; i < count; ++i) {
        auto connection = std::make_unique<ClientConnection>(io_context_);
        
        if (connection->connect(host, port)) {
            connections_.push_back(std::move(connection));
            successful++;
            
            if (performance_monitor_) {
                performance_monitor_->updateConnectionStats(true);
            }
        }
        else {
            if (performance_monitor_) {
                performance_monitor_->updateConnectionStats(false);
            }
        }
    }
    
    std::cout << "Created " << successful << "/" << count << " connections" << std::endl;
    return successful > 0;
}

void MockGameClient::startHeartbeat() {
    running_ = true;
    
    for (auto& connection : connections_) {
        if (connection->isConnected()) {
            connection->startHeartbeat();
        }
    }
}

void MockGameClient::startDataSend() {
    // For pressure testing, we can periodically send test data
    for (auto& connection : connections_) {
        if (connection->isConnected()) {
            connection->sendTestData();
        }
    }
}

void MockGameClient::stop() {
    running_ = false;
    
    for (auto& connection : connections_) {
        connection->disconnect();
    }
    
    connections_.clear();
}

void MockGameClient::setPerformanceMonitor(monitoring::PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
}

uint32_t MockGameClient::getActiveConnections() const {
    uint32_t active = 0;
    for (const auto& connection : connections_) {
        if (connection->isConnected()) {
            active++;
        }
    }
    return active;
}

} // namespace client
} // namespace stress_test