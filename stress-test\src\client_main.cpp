#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <boost/asio.hpp>
#include "client/mock_game_client.h"
#include "monitoring/performance_monitor.h"

void printUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n"
              << "Options:\n"
              << "  --host=<host>           Target host (default: 127.0.0.1)\n"
              << "  --port=<port>           Target port (default: 8080)\n"
              << "  --connections=<n>       Number of connections (default: 1000)\n"
              << "  --threads=<n>           IO thread count (default: hardware_concurrency)\n"
              << "  --duration=<n>          Test duration in seconds (default: 60)\n"
              << "  --help                  Show this help\n";
}

int main(int argc, char* argv[]) {
    try {
        // Default configuration
        std::string host = "127.0.0.1";
        uint16_t port = 8080;
        uint32_t connections = 1000;
        uint32_t thread_count = std::thread::hardware_concurrency();
        uint32_t duration = 60;
        
        // Parse command line arguments
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            
            if (arg == "--help") {
                printUsage(argv[0]);
                return 0;
            }
            else if (arg.find("--host=") == 0) {
                host = arg.substr(7);
            }
            else if (arg.find("--port=") == 0) {
                port = static_cast<uint16_t>(std::stoi(arg.substr(7)));
            }
            else if (arg.find("--connections=") == 0) {
                connections = static_cast<uint32_t>(std::stoi(arg.substr(14)));
            }
            else if (arg.find("--threads=") == 0) {
                thread_count = static_cast<uint32_t>(std::stoi(arg.substr(10)));
            }
            else if (arg.find("--duration=") == 0) {
                duration = static_cast<uint32_t>(std::stoi(arg.substr(11)));
            }
            else {
                std::cerr << "Unknown argument: " << arg << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }
        
        // Validate parameters
        if (thread_count == 0) {
            thread_count = 1;
        }
        
        std::cout << "Starting Mock Game Client\n"
                  << "Target: " << host << ":" << port << "\n"
                  << "Connections: " << connections << "\n"
                  << "IO threads: " << thread_count << "\n"
                  << "Duration: " << duration << "s\n" << std::endl;
        
        // Create IO context and client
        boost::asio::io_context io_context;
        stress_test::client::MockGameClient client(io_context);
        stress_test::monitoring::PerformanceMonitor monitor;
        
        client.setPerformanceMonitor(&monitor);
        
        // Start performance monitoring
        monitor.start();
        
        // Create IO threads FIRST - this is crucial for async operations
        std::cout << "Starting IO threads..." << std::endl;
        std::vector<std::thread> threads;
        for (uint32_t i = 0; i < thread_count; ++i) {
            threads.emplace_back([&io_context]() {
                try {
                    io_context.run();
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception in IO thread: " << e.what() << std::endl;
                }
                catch (...) {
                    std::cerr << "Unknown exception in IO thread" << std::endl;
                }
            });
        }
        
        // Give IO threads time to start
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Create connections
        std::cout << "Creating connections..." << std::endl;
        try {
            if (!client.createConnections(host, port, connections)) {
                std::cerr << "Failed to create any connections" << std::endl;
                // Don't exit immediately, let's see what happens
            }
        }
        catch (const std::exception& e) {
            std::cerr << "Exception creating connections: " << e.what() << std::endl;
            return 1;
        }
        catch (...) {
            std::cerr << "Unknown exception creating connections" << std::endl;
            return 1;
        }
        
        // Start heartbeat
        std::cout << "Starting heartbeat..." << std::endl;
        client.startHeartbeat();
        
        std::cout << "Test running for " << duration << " seconds..." << std::endl;
        
        // Run test for specified duration
        std::this_thread::sleep_for(std::chrono::seconds(duration));
        
        std::cout << "\nStopping test..." << std::endl;
        
        // Stop client
        client.stop();
        
        // Stop IO context
        io_context.stop();
        
        // Wait for threads
        for (auto& thread : threads) {
            thread.join();
        }
        
        // Stop monitoring and show final stats
        monitor.stop();
        
        const auto& metrics = monitor.getMetrics();
        std::cout << "\n=== Final Statistics ===\n"
                  << "Total connections attempted: " << metrics.total_connections_attempted.load() << "\n"
                  << "Total connections succeeded: " << metrics.total_connections_succeeded.load() << "\n"
                  << "Total connections failed: " << metrics.total_connections_failed.load() << "\n"
                  << "Total packets sent: " << metrics.total_packets_sent.load() << "\n"
                  << "Total packets received: " << metrics.total_packets_received.load() << "\n"
                  << "Total bytes sent: " << metrics.total_bytes_sent.load() << "\n"
                  << "Total bytes received: " << metrics.total_bytes_received.load() << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "Client error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}