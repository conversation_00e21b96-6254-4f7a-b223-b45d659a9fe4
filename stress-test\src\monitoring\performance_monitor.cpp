#include "monitoring/performance_monitor.h"
#include <iostream>
#include <iomanip>

namespace stress_test {
namespace monitoring {

PerformanceMonitor::PerformanceMonitor()
    : running_(false)
    , last_connections_succeeded_(0)
    , last_bytes_total_(0) {
    metrics_.test_start_time = std::chrono::steady_clock::now();
    last_calculation_time_ = metrics_.test_start_time;
}

PerformanceMonitor::~PerformanceMonitor() {
    stop();
}

void PerformanceMonitor::start() {
    if (!running_.exchange(true)) {
        monitor_thread_ = std::thread(&PerformanceMonitor::monitorLoop, this);
    }
}

void PerformanceMonitor::stop() {
    if (running_.exchange(false)) {
        if (monitor_thread_.joinable()) {
            monitor_thread_.join();
        }
    }
}

PerformanceMetrics& PerformanceMonitor::getMetrics() {
    return metrics_;
}

const PerformanceMetrics& PerformanceMonitor::getMetrics() const {
    return metrics_;
}

void PerformanceMonitor::updateConnectionStats(bool success) {
    metrics_.total_connections_attempted.fetch_add(1);
    if (success) {
        metrics_.total_connections_succeeded.fetch_add(1);
        metrics_.current_active_connections.fetch_add(1);
    } else {
        metrics_.total_connections_failed.fetch_add(1);
    }
}

void PerformanceMonitor::updateDataStats(uint64_t bytes_sent, uint64_t bytes_received) {
    metrics_.total_bytes_sent.fetch_add(bytes_sent);
    metrics_.total_bytes_received.fetch_add(bytes_received);
}

void PerformanceMonitor::updatePacketStats(uint64_t packets_sent, uint64_t packets_received) {
    metrics_.total_packets_sent.fetch_add(packets_sent);
    metrics_.total_packets_received.fetch_add(packets_received);
}

void PerformanceMonitor::monitorLoop() {
    while (running_) {
        calculateRates();
        printStats();
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void PerformanceMonitor::calculateRates() {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - last_calculation_time_).count();
    
    if (duration >= 1000) { // Calculate every second
        uint64_t current_connections = metrics_.total_connections_succeeded.load();
        uint64_t current_bytes = metrics_.total_bytes_sent.load() + metrics_.total_bytes_received.load();
        
        // Calculate CPS
        double cps = static_cast<double>(current_connections - last_connections_succeeded_) 
                    * 1000.0 / duration;
        metrics_.current_cps.store(cps);
        
        // Calculate throughput in Mbps
        double bytes_per_sec = static_cast<double>(current_bytes - last_bytes_total_) 
                              * 1000.0 / duration;
        double mbps = bytes_per_sec * 8.0 / (1024.0 * 1024.0);
        metrics_.current_throughput_mbps.store(mbps);
        
        // Update for next calculation
        last_connections_succeeded_ = current_connections;
        last_bytes_total_ = current_bytes;
        last_calculation_time_ = now;
    }
}

void PerformanceMonitor::printStats() {
    std::cout << "\r"
              << "Connections: " << metrics_.current_active_connections.load()
              << " | CPS: " << std::fixed << std::setprecision(1) << metrics_.current_cps.load()
              << " | Throughput: " << std::fixed << std::setprecision(2) << metrics_.current_throughput_mbps.load() << " Mbps"
              << " | Sent: " << metrics_.total_packets_sent.load()
              << " | Recv: " << metrics_.total_packets_received.load()
              << std::flush;
}

} // namespace monitoring
} // namespace stress_test