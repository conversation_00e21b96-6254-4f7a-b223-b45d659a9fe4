#include "server/mock_game_server.h"
#include "monitoring/performance_monitor.h"
#include <iostream>
#include <string>

namespace stress_test {
namespace server {

// ServerConnection implementation
std::atomic<uint32_t> ServerConnection::next_connection_id_(1);

ServerConnection::ServerConnection(boost::asio::ip::tcp::socket socket)
    : socket_(std::move(socket))
    , connection_id_(next_connection_id_.fetch_add(1))
    , active_(false)
    , last_activity_(std::chrono::steady_clock::now()) {
}

ServerConnection::~ServerConnection() {
    stop();
}

void ServerConnection::start() {
    active_ = true;
    startRead();
}

void ServerConnection::stop() {
    active_ = false;
    if (socket_.is_open()) {
        try {
            socket_.close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
    }
}

void ServerConnection::startRead() {
    if (!active_ || !socket_.is_open()) {
        return;
    }
    
    auto self = shared_from_this();
    socket_.async_read_some(
        boost::asio::buffer(read_buffer_),
        [this, self](const boost::system::error_code& ec, size_t bytes_transferred) {
            this->handleRead(ec, bytes_transferred);
        });
}

void ServerConnection::handleRead(const boost::system::error_code& error, size_t bytes_transferred) {
    if (!error && bytes_transferred > 0) {
        last_activity_ = std::chrono::steady_clock::now();
        
        // Echo the data back
        echoData(read_buffer_.data(), bytes_transferred);
        
        // Continue reading
        startRead();
    }
    else {
        stop();
    }
}

void ServerConnection::handleWrite(const boost::system::error_code& error, size_t bytes_transferred) {
    if (error) {
        stop();
    }
}

void ServerConnection::echoData(const void* data, size_t size) {
    if (!active_ || !socket_.is_open()) {
        return;
    }
    
    auto self = shared_from_this();
    boost::asio::async_write(socket_,
        boost::asio::buffer(data, size),
        [this, self](const boost::system::error_code& ec, size_t bytes_transferred) {
            this->handleWrite(ec, bytes_transferred);
        });
}

// MockGameServer implementation
MockGameServer::MockGameServer(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , max_connections_(0)
    , running_(false)
    , performance_monitor_(nullptr) {
}

MockGameServer::~MockGameServer() {
    stop();
}

bool MockGameServer::start(const std::string& host, uint16_t port, uint32_t max_connections) {
    try {
        max_connections_ = max_connections;
        
        // Use resolver to handle address conversion
        boost::asio::ip::tcp::resolver resolver(io_context_);
        boost::asio::ip::tcp::resolver::results_type endpoints = 
            resolver.resolve(host, std::to_string(port));
        
        boost::asio::ip::tcp::endpoint endpoint = *endpoints.begin();
        
        acceptor_ = std::make_unique<boost::asio::ip::tcp::acceptor>(io_context_, endpoint);
        acceptor_->set_option(boost::asio::ip::tcp::acceptor::reuse_address(true));
        
        running_ = true;
        startAccept();
        
        std::cout << "Server started on " << host << ":" << port 
                  << ", max connections: " << max_connections << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Server start error: " << e.what() << std::endl;
        return false;
    }
}

void MockGameServer::stop() {
    running_ = false;
    
    if (acceptor_) {
        try {
            acceptor_->close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
        acceptor_.reset();
    }
    
    // Close all connections
    std::lock_guard<std::mutex> lock(connections_mutex_);
    for (auto& pair : connections_) {
        pair.second->stop();
    }
    connections_.clear();
}

void MockGameServer::setPerformanceMonitor(monitoring::PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
}

uint32_t MockGameServer::getActiveConnections() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return static_cast<uint32_t>(connections_.size());
}

void MockGameServer::startAccept() {
    if (!running_ || !acceptor_) {
        return;
    }
    
    // Create socket for new connection
    auto socket = std::make_unique<boost::asio::ip::tcp::socket>(io_context_);
    auto socket_ptr = socket.get();
    
    acceptor_->async_accept(*socket_ptr,
        [this, socket = std::move(socket)](const boost::system::error_code& error) mutable {
            if (!error && running_) {
                // Check connection limit
                if (getActiveConnections() < max_connections_) {
                    // Create connection with moved socket
                    pending_connection_ = std::make_shared<ServerConnection>(std::move(*socket));
                    uint32_t conn_id = pending_connection_->getId();
                    
                    {
                        std::lock_guard<std::mutex> lock(connections_mutex_);
                        connections_[conn_id] = pending_connection_;
                    }
                    
                    pending_connection_->start();
                    
                    if (performance_monitor_) {
                        performance_monitor_->updateConnectionStats(true);
                    }
                }
                else {
                    // Connection limit reached, socket will be automatically closed
                    if (performance_monitor_) {
                        performance_monitor_->updateConnectionStats(false);
                    }
                }
                
                // Continue accepting
                startAccept();
            }
            else if (running_) {
                // Error occurred, but server is still running, try again
                startAccept();
            }
        });
}


void MockGameServer::cleanupConnections() {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    
    auto it = connections_.begin();
    while (it != connections_.end()) {
        // Simple cleanup - remove connections that are no longer active
        ++it;
    }
}

} // namespace server
} // namespace stress_test