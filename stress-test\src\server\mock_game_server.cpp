#include "server/mock_game_server.h"
#include "monitoring/performance_monitor.h"
#include <boost/bind/bind.hpp>
#include <iostream>

namespace stress_test {
namespace server {

// ServerConnection implementation
std::atomic<uint32_t> ServerConnection::next_connection_id_(1);

ServerConnection::ServerConnection(boost::asio::ip::tcp::socket socket)
    : socket_(std::move(socket))
    , connection_id_(next_connection_id_.fetch_add(1))
    , active_(false)
    , last_activity_(std::chrono::steady_clock::now()) {
}

ServerConnection::~ServerConnection() {
    stop();
}

void ServerConnection::start() {
    active_ = true;
    startRead();
}

void ServerConnection::stop() {
    active_ = false;
    if (socket_.is_open()) {
        try {
            socket_.close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
    }
}

void ServerConnection::startRead() {
    if (!active_ || !socket_.is_open()) {
        return;
    }
    
    socket_.async_read_some(
        boost::asio::buffer(read_buffer_),
        boost::bind(&ServerConnection::handleRead, shared_from_this(),
            boost::asio::placeholders::error,
            boost::asio::placeholders::bytes_transferred));
}

void ServerConnection::handleRead(const boost::system::error_code& error, size_t bytes_transferred) {
    if (!error && bytes_transferred > 0) {
        last_activity_ = std::chrono::steady_clock::now();
        
        // Echo the data back
        echoData(read_buffer_.data(), bytes_transferred);
        
        // Continue reading
        startRead();
    }
    else {
        stop();
    }
}

void ServerConnection::handleWrite(const boost::system::error_code& error, size_t bytes_transferred) {
    if (error) {
        stop();
    }
}

void ServerConnection::echoData(const void* data, size_t size) {
    if (!active_ || !socket_.is_open()) {
        return;
    }
    
    boost::asio::async_write(socket_,
        boost::asio::buffer(data, size),
        boost::bind(&ServerConnection::handleWrite, shared_from_this(),
            boost::asio::placeholders::error,
            boost::asio::placeholders::bytes_transferred));
}

// MockGameServer implementation
MockGameServer::MockGameServer(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , max_connections_(0)
    , running_(false)
    , performance_monitor_(nullptr) {
}

MockGameServer::~MockGameServer() {
    stop();
}

bool MockGameServer::start(const std::string& host, uint16_t port, uint32_t max_connections) {
    try {
        max_connections_ = max_connections;
        
        boost::asio::ip::tcp::endpoint endpoint(
            boost::asio::ip::address::from_string(host), port);
        
        acceptor_ = std::make_unique<boost::asio::ip::tcp::acceptor>(io_context_, endpoint);
        acceptor_->set_option(boost::asio::ip::tcp::acceptor::reuse_address(true));
        
        running_ = true;
        startAccept();
        
        std::cout << "Server started on " << host << ":" << port 
                  << ", max connections: " << max_connections << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Server start error: " << e.what() << std::endl;
        return false;
    }
}

void MockGameServer::stop() {
    running_ = false;
    
    if (acceptor_) {
        try {
            acceptor_->close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
        acceptor_.reset();
    }
    
    // Close all connections
    std::lock_guard<std::mutex> lock(connections_mutex_);
    for (auto& pair : connections_) {
        pair.second->stop();
    }
    connections_.clear();
}

void MockGameServer::setPerformanceMonitor(monitoring::PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
}

uint32_t MockGameServer::getActiveConnections() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return static_cast<uint32_t>(connections_.size());
}

void MockGameServer::startAccept() {
    if (!running_ || !acceptor_) {
        return;
    }
    
    // Create new connection
    pending_connection_ = std::make_shared<ServerConnection>(
        boost::asio::ip::tcp::socket(io_context_));
    
    acceptor_->async_accept(pending_connection_->socket_,
        boost::bind(&MockGameServer::handleAccept, this,
            boost::asio::placeholders::error));
}

void MockGameServer::handleAccept(const boost::system::error_code& error) {
    if (!error && running_) {
        // Check connection limit
        if (getActiveConnections() < max_connections_) {
            uint32_t conn_id = pending_connection_->getId();
            
            {
                std::lock_guard<std::mutex> lock(connections_mutex_);
                connections_[conn_id] = pending_connection_;
            }
            
            pending_connection_->start();
            
            if (performance_monitor_) {
                performance_monitor_->updateConnectionStats(true);
            }
        }
        else {
            // Connection limit reached, close the new connection
            pending_connection_->stop();
            
            if (performance_monitor_) {
                performance_monitor_->updateConnectionStats(false);
            }
        }
        
        // Continue accepting
        startAccept();
    }
    else if (running_) {
        // Error occurred, but server is still running, try again
        startAccept();
    }
}

void MockGameServer::cleanupConnections() {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    
    auto it = connections_.begin();
    while (it != connections_.end()) {
        if (!it->second->socket_.is_open()) {
            it = connections_.erase(it);
        }
        else {
            ++it;
        }
    }
}

} // namespace server
} // namespace stress_test