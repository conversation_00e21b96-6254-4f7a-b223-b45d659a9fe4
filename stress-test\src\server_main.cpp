#include <iostream>
#include <string>
#include <thread>
#include <boost/asio.hpp>
#include "server/mock_game_server.h"
#include "monitoring/performance_monitor.h"

void printUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n"
              << "Options:\n"
              << "  --host=<host>           Server host (default: 0.0.0.0)\n"
              << "  --port=<port>           Server port (default: 8080)\n"
              << "  --max-connections=<n>   Maximum connections (default: 10000)\n"
              << "  --threads=<n>           IO thread count (default: hardware_concurrency)\n"
              << "  --help                  Show this help\n";
}

int main(int argc, char* argv[]) {
    try {
        // Default configuration
        std::string host = "0.0.0.0";
        uint16_t port = 8080;
        uint32_t max_connections = 10000;
        uint32_t thread_count = std::thread::hardware_concurrency();
        
        // Parse command line arguments
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            
            if (arg == "--help") {
                printUsage(argv[0]);
                return 0;
            }
            else if (arg.find("--host=") == 0) {
                host = arg.substr(7);
            }
            else if (arg.find("--port=") == 0) {
                port = static_cast<uint16_t>(std::stoi(arg.substr(7)));
            }
            else if (arg.find("--max-connections=") == 0) {
                max_connections = static_cast<uint32_t>(std::stoi(arg.substr(18)));
            }
            else if (arg.find("--threads=") == 0) {
                thread_count = static_cast<uint32_t>(std::stoi(arg.substr(10)));
            }
            else {
                std::cerr << "Unknown argument: " << arg << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }
        
        // Validate parameters
        if (thread_count == 0) {
            thread_count = 1;
        }
        
        std::cout << "Starting Mock Game Server\n"
                  << "Host: " << host << "\n"
                  << "Port: " << port << "\n"
                  << "Max connections: " << max_connections << "\n"
                  << "IO threads: " << thread_count << "\n" << std::endl;
        
        // Create IO context and server
        boost::asio::io_context io_context;
        stress_test::server::MockGameServer server(io_context);
        stress_test::monitoring::PerformanceMonitor monitor;
        
        server.setPerformanceMonitor(&monitor);
        
        // Start server
        if (!server.start(host, port, max_connections)) {
            std::cerr << "Failed to start server" << std::endl;
            return 1;
        }
        
        // Start performance monitoring
        monitor.start();
        
        // Create IO threads
        std::vector<std::thread> threads;
        for (uint32_t i = 0; i < thread_count; ++i) {
            threads.emplace_back([&io_context]() {
                io_context.run();
            });
        }
        
        std::cout << "Server running. Press Ctrl+C to stop..." << std::endl;
        
        // Wait for threads
        for (auto& thread : threads) {
            thread.join();
        }
        
        // Stop monitoring
        monitor.stop();
        
        std::cout << "\nServer stopped." << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "Server error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}