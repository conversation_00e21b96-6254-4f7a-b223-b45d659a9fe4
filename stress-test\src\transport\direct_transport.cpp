#include "transport/direct_transport.h"
#include <string>
#include <iostream>

namespace stress_test {
namespace transport {

DirectTransport::DirectTransport(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , socket_(io_context)
    , connected_(false) {
}

DirectTransport::~DirectTransport() {
    disconnect();
}

bool DirectTransport::connect(const std::string& host, uint16_t port) {
    try {
        boost::asio::ip::tcp::resolver resolver(io_context_);
        auto endpoints = resolver.resolve(host, std::to_string(port));
        
        // Use shared_from_this to ensure object lifetime in async callbacks
        auto self = shared_from_this();
        boost::asio::async_connect(socket_, endpoints,
            [self, this](const boost::system::error_code& ec, const boost::asio::ip::tcp::endpoint& endpoint) {
                try {
                    this->handleConnect(ec);
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception in handleConnect: " << e.what() << std::endl;
                    connected_ = false;
                }
                catch (...) {
                    std::cerr << "Unknown exception in handleConnect" << std::endl;
                    connected_ = false;
                }
            });
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in connect: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in connect" << std::endl;
        return false;
    }
}

bool DirectTransport::send(const void* data, size_t size) {
    if (!connected_ || !socket_.is_open()) {
        return false;
    }
    
    try {
        auto self = shared_from_this();
        boost::asio::async_write(socket_,
            boost::asio::buffer(data, size),
            [self, this](const boost::system::error_code& ec, size_t bytes_transferred) {
                try {
                    this->handleSend(ec, bytes_transferred);
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception in handleSend: " << e.what() << std::endl;
                    connected_ = false;
                }
                catch (...) {
                    std::cerr << "Unknown exception in handleSend" << std::endl;
                    connected_ = false;
                }
            });
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in send: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in send" << std::endl;
        return false;
    }
}

void DirectTransport::setReceiveCallback(ReceiveCallback callback) {
    receive_callback_ = callback;
}

void DirectTransport::disconnect() {
    connected_ = false;
    if (socket_.is_open()) {
        try {
            socket_.close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
    }
}

bool DirectTransport::isConnected() const {
    return connected_ && socket_.is_open();
}

void DirectTransport::startReceive() {
    if (!connected_ || !socket_.is_open()) {
        return;
    }
    
    auto self = shared_from_this();
    socket_.async_read_some(
        boost::asio::buffer(receive_buffer_),
        [self, this](const boost::system::error_code& ec, size_t bytes_transferred) {
            try {
                this->handleReceive(ec, bytes_transferred);
            }
            catch (const std::exception& e) {
                std::cerr << "Exception in handleReceive: " << e.what() << std::endl;
                connected_ = false;
            }
            catch (...) {
                std::cerr << "Unknown exception in handleReceive" << std::endl;
                connected_ = false;
            }
        });
}

void DirectTransport::handleConnect(const boost::system::error_code& error) {
    if (!error) {
        connected_ = true;
        std::cout << "DirectTransport: Connection established successfully" << std::endl;
        try {
            startReceive();
        }
        catch (const std::exception& e) {
            std::cerr << "Exception in startReceive: " << e.what() << std::endl;
            connected_ = false;
        }
        catch (...) {
            std::cerr << "Unknown exception in startReceive" << std::endl;
            connected_ = false;
        }
    }
    else {
        connected_ = false;
        std::cerr << "DirectTransport: Connection failed: " << error.message() << std::endl;
    }
}

void DirectTransport::handleSend(const boost::system::error_code& error, size_t bytes_transferred) {
    // For high performance testing, we don't need complex error handling
    // Just ensure connection is still valid
    if (error) {
        connected_ = false;
    }
}

void DirectTransport::handleReceive(const boost::system::error_code& error, size_t bytes_transferred) {
    if (!error && bytes_transferred > 0) {
        if (receive_callback_) {
            receive_callback_(receive_buffer_.data(), bytes_transferred);
        }
        
        // Continue receiving
        startReceive();
    }
    else {
        connected_ = false;
    }
}

} // namespace transport
} // namespace stress_test