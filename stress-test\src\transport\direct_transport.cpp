#include "transport/direct_transport.h"
#include <string>
#include <iostream>

namespace stress_test {
namespace transport {

DirectTransport::DirectTransport(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , socket_(io_context)
    , connected_(false) {
}

DirectTransport::~DirectTransport() {
    disconnect();
}

bool DirectTransport::connect(const std::string& host, uint16_t port) {
    try {
        std::cout << "DirectTransport: Starting connection to " << host << ":" << port << std::endl;
        
        boost::asio::ip::tcp::resolver resolver(io_context_);
        auto endpoints = resolver.resolve(host, std::to_string(port));
        
        std::cout << "DirectTransport: DNS resolution completed, attempting async_connect" << std::endl;
        
        // Use shared_from_this to ensure object lifetime in async callbacks
        auto self = shared_from_this();
        boost::asio::async_connect(socket_, endpoints,
            [self, this, host, port](const boost::system::error_code& ec, const boost::asio::ip::tcp::endpoint& endpoint) {
                std::cout << "DirectTransport: async_connect callback triggered for " << host << ":" << port 
                         << ", error_code: " << ec.value() << " (" << ec.message() << ")" << std::endl;
                try {
                    this->handleConnect(ec);
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception in handleConnect: " << e.what() << std::endl;
                    connected_ = false;
                }
                catch (...) {
                    std::cerr << "Unknown exception in handleConnect" << std::endl;
                    connected_ = false;
                }
            });
        
        std::cout << "DirectTransport: async_connect initiated successfully" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in connect: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in connect" << std::endl;
        return false;
    }
}

bool DirectTransport::send(const void* data, size_t size) {
    if (!connected_ || !socket_.is_open()) {
        return false;
    }
    
    try {
        auto self = shared_from_this();
        boost::asio::async_write(socket_,
            boost::asio::buffer(data, size),
            [self, this](const boost::system::error_code& ec, size_t bytes_transferred) {
                try {
                    this->handleSend(ec, bytes_transferred);
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception in handleSend: " << e.what() << std::endl;
                    connected_ = false;
                }
                catch (...) {
                    std::cerr << "Unknown exception in handleSend" << std::endl;
                    connected_ = false;
                }
            });
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in send: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in send" << std::endl;
        return false;
    }
}

void DirectTransport::setReceiveCallback(ReceiveCallback callback) {
    receive_callback_ = callback;
}

void DirectTransport::disconnect() {
    connected_ = false;
    if (socket_.is_open()) {
        try {
            socket_.close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
    }
}

bool DirectTransport::isConnected() const {
    bool socket_open = socket_.is_open();
    bool connected_flag = connected_;
    
    // Additional diagnostic info for debugging
    static int debug_counter = 0;
    if (++debug_counter % 50 == 1) { // Log every 50th call to avoid spam
        std::cout << "DirectTransport::isConnected() - socket_open: " << socket_open 
                  << ", connected_flag: " << connected_flag << std::endl;
    }
    
    return connected_flag && socket_open;
}

void DirectTransport::startReceive() {
    if (!connected_ || !socket_.is_open()) {
        return;
    }
    
    auto self = shared_from_this();
    socket_.async_read_some(
        boost::asio::buffer(receive_buffer_),
        [self, this](const boost::system::error_code& ec, size_t bytes_transferred) {
            try {
                this->handleReceive(ec, bytes_transferred);
            }
            catch (const std::exception& e) {
                std::cerr << "Exception in handleReceive: " << e.what() << std::endl;
                connected_ = false;
            }
            catch (...) {
                std::cerr << "Unknown exception in handleReceive" << std::endl;
                connected_ = false;
            }
        });
}

void DirectTransport::handleConnect(const boost::system::error_code& error) {
    std::cout << "DirectTransport::handleConnect called with error_code: " << error.value() 
              << " (" << error.message() << ")" << std::endl;
              
    if (!error) {
        connected_ = true;
        std::cout << "DirectTransport: Connection established successfully! Socket is open: " 
                  << socket_.is_open() << std::endl;
        
        // Log local and remote endpoints for debugging
        try {
            auto local_endpoint = socket_.local_endpoint();
            auto remote_endpoint = socket_.remote_endpoint();
            std::cout << "DirectTransport: Local endpoint: " << local_endpoint 
                      << ", Remote endpoint: " << remote_endpoint << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "DirectTransport: Could not get endpoint info: " << e.what() << std::endl;
        }
        
        try {
            startReceive();
            std::cout << "DirectTransport: startReceive() called successfully" << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Exception in startReceive: " << e.what() << std::endl;
            connected_ = false;
        }
        catch (...) {
            std::cerr << "Unknown exception in startReceive" << std::endl;
            connected_ = false;
        }
    }
    else {
        connected_ = false;
        std::cerr << "DirectTransport: Connection failed: " << error.message() 
                  << " (error_code: " << error.value() << ")" << std::endl;
    }
}

void DirectTransport::handleSend(const boost::system::error_code& error, size_t bytes_transferred) {
    // For high performance testing, we don't need complex error handling
    // Just ensure connection is still valid
    if (error) {
        connected_ = false;
    }
}

void DirectTransport::handleReceive(const boost::system::error_code& error, size_t bytes_transferred) {
    if (!error && bytes_transferred > 0) {
        if (receive_callback_) {
            receive_callback_(receive_buffer_.data(), bytes_transferred);
        }
        
        // Continue receiving
        startReceive();
    }
    else {
        connected_ = false;
    }
}

} // namespace transport
} // namespace stress_test