#include "transport/direct_transport.h"
#include <string>

namespace stress_test {
namespace transport {

DirectTransport::DirectTransport(boost::asio::io_context& io_context)
    : io_context_(io_context)
    , socket_(io_context)
    , connected_(false) {
}

DirectTransport::~DirectTransport() {
    disconnect();
}

bool DirectTransport::connect(const std::string& host, uint16_t port) {
    try {
        boost::asio::ip::tcp::resolver resolver(io_context_);
        auto endpoints = resolver.resolve(host, std::to_string(port));
        
        boost::asio::async_connect(socket_, endpoints,
            [this](const boost::system::error_code& ec, const boost::asio::ip::tcp::endpoint&) {
                this->handleConnect(ec);
            });
        
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool DirectTransport::send(const void* data, size_t size) {
    if (!connected_ || !socket_.is_open()) {
        return false;
    }
    
    try {
        boost::asio::async_write(socket_,
            boost::asio::buffer(data, size),
            [this](const boost::system::error_code& ec, size_t bytes_transferred) {
                this->handleSend(ec, bytes_transferred);
            });
        
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

void DirectTransport::setReceiveCallback(ReceiveCallback callback) {
    receive_callback_ = callback;
}

void DirectTransport::disconnect() {
    connected_ = false;
    if (socket_.is_open()) {
        try {
            socket_.close();
        }
        catch (const std::exception&) {
            // Ignore close errors
        }
    }
}

bool DirectTransport::isConnected() const {
    return connected_ && socket_.is_open();
}

void DirectTransport::startReceive() {
    if (!connected_ || !socket_.is_open()) {
        return;
    }
    
    socket_.async_read_some(
        boost::asio::buffer(receive_buffer_),
        [this](const boost::system::error_code& ec, size_t bytes_transferred) {
            this->handleReceive(ec, bytes_transferred);
        });
}

void DirectTransport::handleConnect(const boost::system::error_code& error) {
    if (!error) {
        connected_ = true;
        startReceive();
    }
    else {
        connected_ = false;
    }
}

void DirectTransport::handleSend(const boost::system::error_code& error, size_t bytes_transferred) {
    // For high performance testing, we don't need complex error handling
    // Just ensure connection is still valid
    if (error) {
        connected_ = false;
    }
}

void DirectTransport::handleReceive(const boost::system::error_code& error, size_t bytes_transferred) {
    if (!error && bytes_transferred > 0) {
        if (receive_callback_) {
            receive_callback_(receive_buffer_.data(), bytes_transferred);
        }
        
        // Continue receiving
        startReceive();
    }
    else {
        connected_ = false;
    }
}

} // namespace transport
} // namespace stress_test