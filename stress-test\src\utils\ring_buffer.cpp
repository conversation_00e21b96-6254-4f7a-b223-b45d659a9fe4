#include "utils/ring_buffer.h"
#include <cstring>
#include <algorithm>

namespace stress_test {
namespace utils {

RingBuffer::RingBuffer(size_t capacity)
    : buffer_(capacity)
    , read_pos_(0)
    , write_pos_(0)
    , capacity_(capacity) {
}

size_t RingBuffer::write(const void* data, size_t size) {
    if (!data || size == 0) {
        return 0;
    }
    
    size_t available = writeAvailable();
    size_t to_write = std::min(size, available);
    
    if (to_write == 0) {
        return 0;
    }
    
    size_t current_write_pos = write_pos_.load();
    const uint8_t* src = static_cast<const uint8_t*>(data);
    
    // Handle wrap around
    if (current_write_pos + to_write > capacity_) {
        size_t first_part = capacity_ - current_write_pos;
        std::memcpy(&buffer_[current_write_pos], src, first_part);
        std::memcpy(&buffer_[0], src + first_part, to_write - first_part);
    } else {
        std::memcpy(&buffer_[current_write_pos], src, to_write);
    }
    
    write_pos_.store((current_write_pos + to_write) % capacity_);
    return to_write;
}

size_t RingBuffer::read(void* data, size_t size) {
    if (!data || size == 0) {
        return 0;
    }
    
    size_t available = readAvailable();
    size_t to_read = std::min(size, available);
    
    if (to_read == 0) {
        return 0;
    }
    
    size_t current_read_pos = read_pos_.load();
    uint8_t* dest = static_cast<uint8_t*>(data);
    
    // Handle wrap around
    if (current_read_pos + to_read > capacity_) {
        size_t first_part = capacity_ - current_read_pos;
        std::memcpy(dest, &buffer_[current_read_pos], first_part);
        std::memcpy(dest + first_part, &buffer_[0], to_read - first_part);
    } else {
        std::memcpy(dest, &buffer_[current_read_pos], to_read);
    }
    
    read_pos_.store((current_read_pos + to_read) % capacity_);
    return to_read;
}

size_t RingBuffer::writeAvailable() const {
    size_t read_pos = read_pos_.load();
    size_t write_pos = write_pos_.load();
    
    if (write_pos >= read_pos) {
        return capacity_ - write_pos + read_pos - 1;
    } else {
        return read_pos - write_pos - 1;
    }
}

size_t RingBuffer::readAvailable() const {
    size_t read_pos = read_pos_.load();
    size_t write_pos = write_pos_.load();
    
    if (write_pos >= read_pos) {
        return write_pos - read_pos;
    } else {
        return capacity_ - read_pos + write_pos;
    }
}

bool RingBuffer::empty() const {
    return readAvailable() == 0;
}

bool RingBuffer::full() const {
    return writeAvailable() == 0;
}

void RingBuffer::clear() {
    read_pos_.store(0);
    write_pos_.store(0);
}

} // namespace utils
} // namespace stress_test