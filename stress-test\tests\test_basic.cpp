#include <iostream>
#include "protocol/simple_protocol.h"
#include "monitoring/performance_monitor.h"

int main() {
    try {
        std::cout << "Testing basic components..." << std::endl;
        
        // Test protocol definitions
        stress_test::protocol::PacketHeader header;
        header.packet_type = stress_test::protocol::PACKET_TYPE_HEARTBEAT;
        header.packet_length = sizeof(header);
        header.connection_id = 1;
        header.sequence_num = 1;
        
        std::cout << "✓ Protocol structures work" << std::endl;
        
        // Test performance monitor
        stress_test::monitoring::PerformanceMonitor monitor;
        monitor.updateConnectionStats(true);
        monitor.updateDataStats(100, 100);
        
        std::cout << "✓ Performance monitor works" << std::endl;
        
        std::cout << "✓ All basic tests passed!" << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}