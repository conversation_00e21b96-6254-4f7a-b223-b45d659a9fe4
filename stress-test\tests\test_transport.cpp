#include <iostream>
#include <boost/asio.hpp>
#include "transport/direct_transport.h"

int main() {
    try {
        std::cout << "Testing DirectTransport..." << std::endl;
        
        boost::asio::io_context io_context;
        stress_test::transport::DirectTransport transport(io_context);
        
        // Test basic interface
        if (!transport.isConnected()) {
            std::cout << "✓ Initial state: not connected" << std::endl;
        }
        
        // Test callback setting
        transport.setReceiveCallback([](const void* data, size_t size) {
            std::cout << "Received " << size << " bytes" << std::endl;
        });
        
        std::cout << "✓ DirectTransport basic test passed" << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}