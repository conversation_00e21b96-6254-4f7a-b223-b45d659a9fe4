Windows PowerShell
版权所有（C） Microsoft Corporation。保留所有权利。

安装最新的 PowerShell，了解新功能和改进！https://aka.ms/PSWindows

PS C:\Users\<USER>\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-client.exe --connections=100 --duration=60
Starting Mock Game Client
Target: 127.0.0.1:8080
Connections: 100
IO threads: 16
Duration: 60s

Starting IO threads...
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0Creating connections...
Connection 1: Attempting to connect to 127.0.0.1:8080
Connection 1: Transport connect initiated
Connection 2: Attempting to connect to 127.0.0.1:8080
Connection 2: Transport connect initiated
Connection 3: Attempting to connect to 127.0.0.1:8080
Connection 3: Transport connect initiated
Connection 4: Attempting to connect to 127.0.0.1:8080
Connection 4: Transport connect initiated
Connection 5: Attempting to connect to 127.0.0.1:8080
Connection 5: Transport connect initiated
Connection 6: Attempting to connect to 127.0.0.1:8080
Connection 6: Transport connect initiated
Connection 7: Attempting to connect to 127.0.0.1:8080
Connection 7: Transport connect initiated
Connection 8: Attempting to connect to 127.0.0.1:8080
Connection 8: Transport connect initiated
Connection 9: Attempting to connect to 127.0.0.1:8080
Connection 9: Transport connect initiated
Connection 10: Attempting to connect to 127.0.0.1:8080
Connection 10: Transport connect initiated
Connection 11: Attempting to connect to 127.0.0.1:8080
Connection 11: Transport connect initiated
Connection 12: Attempting to connect to 127.0.0.1:8080
Connection 12: Transport connect initiated
Connection 13: Attempting to connect to 127.0.0.1:8080
Connection 13: Transport connect initiated
Connection 14: Attempting to connect to 127.0.0.1:8080
Connection 14: Transport connect initiated
Connection 15: Attempting to connect to 127.0.0.1:8080
Connection 15: Transport connect initiated
Connection 16: Attempting to connect to 127.0.0.1:8080
Connection 16: Transport connect initiated
Connection 17: Attempting to connect to 127.0.0.1:8080
Connection 17: Transport connect initiated
Connection 18: Attempting to connect to 127.0.0.1:8080
Connection 18: Transport connect initiated
Connection 19: Attempting to connect to 127.0.0.1:8080
Connection 19: Transport connect initiated
Connection 20: Attempting to connect to 127.0.0.1:8080
Connection 20: Transport connect initiated
Connection 21: Attempting to connect to 127.0.0.1:8080
Connection 21: Transport connect initiated
Connection 22: Attempting to connect to 127.0.0.1:8080
Connection 22: Transport connect initiated
Connection 23: Attempting to connect to 127.0.0.1:8080
Connection 23: Transport connect initiated
Connection 24: Attempting to connect to 127.0.0.1:8080
Connection 24: Transport connect initiated
Connection 25: Attempting to connect to 127.0.0.1:8080
Connection 25: Transport connect initiated
Connection 26: Attempting to connect to 127.0.0.1:8080
Connection 26: Transport connect initiated
Connection 27: Attempting to connect to 127.0.0.1:8080
Connection 27: Transport connect initiated
Connection 28: Attempting to connect to 127.0.0.1:8080
Connection 28: Transport connect initiated
Connection 29: Attempting to connect to 127.0.0.1:8080
Connection 29: Transport connect initiated
Connection 30: Attempting to connect to 127.0.0.1:8080
Connection 30: Transport connect initiated
Connection 31: Attempting to connect to 127.0.0.1:8080
Connection 31: Transport connect initiated
Connection 32: Attempting to connect to 127.0.0.1:8080
Connection 32: Transport connect initiated
Connection 33: Attempting to connect to 127.0.0.1:8080
Connection 33: Transport connect initiated
Connection 34: Attempting to connect to 127.0.0.1:8080
Connection 34: Transport connect initiated
Connection 35: Attempting to connect to 127.0.0.1:8080
Connection 35: Transport connect initiated
Connection 36: Attempting to connect to 127.0.0.1:8080
Connection 36: Transport connect initiated
Connection 37: Attempting to connect to 127.0.0.1:8080
Connection 37: Transport connect initiated
Connection 38: Attempting to connect to 127.0.0.1:8080
Connection 38: Transport connect initiated
Connection 39: Attempting to connect to 127.0.0.1:8080
Connection 39: Transport connect initiated
Connection 40: Attempting to connect to 127.0.0.1:8080
Connection 40: Transport connect initiated
Connection 41: Attempting to connect to 127.0.0.1:8080
Connection 41: Transport connect initiated
Connection 42: Attempting to connect to 127.0.0.1:8080
Connection 42: Transport connect initiated
Connection 43: Attempting to connect to 127.0.0.1:8080
Connection 43: Transport connect initiated
Connection 44: Attempting to connect to 127.0.0.1:8080
Connection 44: Transport connect initiated
Connection 45: Attempting to connect to 127.0.0.1:8080
Connection 45: Transport connect initiated
Connection 46: Attempting to connect to 127.0.0.1:8080
Connection 46: Transport connect initiated
Connection 47: Attempting to connect to 127.0.0.1:8080
Connection 47: Transport connect initiated
Connection 48: Attempting to connect to 127.0.0.1:8080
Connection 48: Transport connect initiated
Connection 49: Attempting to connect to 127.0.0.1:8080
Connection 49: Transport connect initiated
Connection 50: Attempting to connect to 127.0.0.1:8080
Connection 50: Transport connect initiated
Connection 51: Attempting to connect to 127.0.0.1:8080
Connection 51: Transport connect initiated
Connection 52: Attempting to connect to 127.0.0.1:8080
Connection 52: Transport connect initiated
Connection 53: Attempting to connect to 127.0.0.1:8080
Connection 53: Transport connect initiated
Connection 54: Attempting to connect to 127.0.0.1:8080
Connection 54: Transport connect initiated
Connection 55: Attempting to connect to 127.0.0.1:8080
Connection 55: Transport connect initiated
Connection 56: Attempting to connect to 127.0.0.1:8080
Connection 56: Transport connect initiated
Connection 57: Attempting to connect to 127.0.0.1:8080
Connection 57: Transport connect initiated
Connection 58: Attempting to connect to 127.0.0.1:8080
Connection 58: Transport connect initiated
Connection 59: Attempting to connect to 127.0.0.1:8080
Connection 59: Transport connect initiated
Connection 60: Attempting to connect to 127.0.0.1:8080
Connection 60: Transport connect initiated
Connection 61: Attempting to connect to 127.0.0.1:8080
Connection 61: Transport connect initiated
Connection 62: Attempting to connect to 127.0.0.1:8080
Connection 62: Transport connect initiated
Connection 63: Attempting to connect to 127.0.0.1:8080
Connection 63: Transport connect initiated
Connection 64: Attempting to connect to 127.0.0.1:8080
Connection 64: Transport connect initiated
Connection 65: Attempting to connect to 127.0.0.1:8080
Connection 65: Transport connect initiated
Connection 66: Attempting to connect to 127.0.0.1:8080
Connection 66: Transport connect initiated
Connection 67: Attempting to connect to 127.0.0.1:8080
Connection 67: Transport connect initiated
Connection 68: Attempting to connect to 127.0.0.1:8080
Connection 68: Transport connect initiated
Connection 69: Attempting to connect to 127.0.0.1:8080
Connection 69: Transport connect initiated
Connection 70: Attempting to connect to 127.0.0.1:8080
Connection 70: Transport connect initiated
Connection 71: Attempting to connect to 127.0.0.1:8080
Connection 71: Transport connect initiated
Connection 72: Attempting to connect to 127.0.0.1:8080
Connection 72: Transport connect initiated
Connection 73: Attempting to connect to 127.0.0.1:8080
Connection 73: Transport connect initiated
Connection 74: Attempting to connect to 127.0.0.1:8080
Connection 74: Transport connect initiated
Connection 75: Attempting to connect to 127.0.0.1:8080
Connection 75: Transport connect initiated
Connection 76: Attempting to connect to 127.0.0.1:8080
Connection 76: Transport connect initiated
Connection 77: Attempting to connect to 127.0.0.1:8080
Connection 77: Transport connect initiated
Connection 78: Attempting to connect to 127.0.0.1:8080
Connection 78: Transport connect initiated
Connection 79: Attempting to connect to 127.0.0.1:8080
Connection 79: Transport connect initiated
Connection 80: Attempting to connect to 127.0.0.1:8080
Connection 80: Transport connect initiated
Connection 81: Attempting to connect to 127.0.0.1:8080
Connection 81: Transport connect initiated
Connection 82: Attempting to connect to 127.0.0.1:8080
Connection 82: Transport connect initiated
Connection 83: Attempting to connect to 127.0.0.1:8080
Connection 83: Transport connect initiated
Connection 84: Attempting to connect to 127.0.0.1:8080
Connection 84: Transport connect initiated
Connection 85: Attempting to connect to 127.0.0.1:8080
Connection 85: Transport connect initiated
Connection 86: Attempting to connect to 127.0.0.1:8080
Connection 86: Transport connect initiated
Connection 87: Attempting to connect to 127.0.0.1:8080
Connection 87: Transport connect initiated
Connection 88: Attempting to connect to 127.0.0.1:8080
Connection 88: Transport connect initiated
Connection 89: Attempting to connect to 127.0.0.1:8080
Connection 89: Transport connect initiated
Connection 90: Attempting to connect to 127.0.0.1:8080
Connection 90: Transport connect initiated
Connection 91: Attempting to connect to 127.0.0.1:8080
Connection 91: Transport connect initiated
Connection 92: Attempting to connect to 127.0.0.1:8080
Connection 92: Transport connect initiated
Connection 93: Attempting to connect to 127.0.0.1:8080
Connection 93: Transport connect initiated
Connection 94: Attempting to connect to 127.0.0.1:8080
Connection 94: Transport connect initiated
Connection 95: Attempting to connect to 127.0.0.1:8080
Connection 95: Transport connect initiated
Connection 96: Attempting to connect to 127.0.0.1:8080
Connection 96: Transport connect initiated
Connection 97: Attempting to connect to 127.0.0.1:8080
Connection 97: Transport connect initiated
Connection 98: Attempting to connect to 127.0.0.1:8080
Connection 98: Transport connect initiated
Connection 99: Attempting to connect to 127.0.0.1:8080
Connection 99: Transport connect initiated
Connection 100: Attempting to connect to 127.0.0.1:8080
Connection 100: Transport connect initiated
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0Created 0/100 connections
Failed to create any connections
Starting heartbeat...
Test running for 60 seconds...
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0
Stopping test...

=== Final Statistics ===
Total connections attempted: 0
Total connections succeeded: 0
Total connections failed: 0
Total packets sent: 0
Total packets received: 0
Total bytes sent: 0
Total bytes received: 0
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin>

Windows PowerShell
版权所有（C） Microsoft Corporation。保留所有权利。

安装最新的 PowerShell，了解新功能和改进！https://aka.ms/PSWindows

PS C:\Users\<USER>\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-server.exe --port=8080 --max-connections=1000
Starting Mock Game Server
Host: 0.0.0.0
Port: 8080
Max connections: 1000
IO threads: 16

Server started on 0.0.0.0:8080, max connections: 1000
Server running. Press Ctrl+C to stop...
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0Server: Accepted new connection 1
Server: Accepted new connection 2
Server: Accepted new connection 3
Server: Accepted new connection 4
Server: Accepted new connection 5
Server: Accepted new connection 6
Server: Accepted new connection 7
Server: Accepted new connection 8
Server: Accepted new connection 9
Server: Accepted new connection 10
Server: Accepted new connection 11
Server: Accepted new connection 12
Server: Accepted new connection 13
Server: Accepted new connection 14
Server: Accepted new connection 15
Server: Accepted new connection 16
Server: Accepted new connection 17
Server: Accepted new connection 18
Server: Accepted new connection 19
Server: Accepted new connection 20
Server: Accepted new connection 21
Server: Accepted new connection 22
Server: Accepted new connection 23
Server: Accepted new connection 24
Server: Accepted new connection 25
Server: Accepted new connection 26
Server: Accepted new connection 27
Server: Accepted new connection 28
Server: Accepted new connection 29
Server: Accepted new connection 30
Server: Accepted new connection 31
Server: Accepted new connection 32
Server: Accepted new connection 33
Server: Accepted new connection 34
Server: Accepted new connection 35
Server: Accepted new connection 36
Server: Accepted new connection 37
Server: Accepted new connection 38
Server: Accepted new connection 39
Server: Accepted new connection 40
Server: Accepted new connection 41
Server: Accepted new connection 42
Server: Accepted new connection 43
Server: Accepted new connection 44
Server: Accepted new connection 45
Server: Accepted new connection 46
Server: Accepted new connection 47
Server: Accepted new connection 48
Server: Accepted new connection 49
Server: Accepted new connection 50
Server: Accepted new connection 51
Server: Accepted new connection 52
Server: Accepted new connection 53
Server: Accepted new connection 54
Server: Accepted new connection 55
Server: Accepted new connection 56
Server: Accepted new connection 57
Server: Accepted new connection 58
Server: Accepted new connection 59
Server: Accepted new connection 60
Server: Accepted new connection 61
Server: Accepted new connection 62
Server: Accepted new connection 63
Server: Accepted new connection 64
Server: Accepted new connection 65
Server: Accepted new connection 66
Server: Accepted new connection 67
Server: Accepted new connection 68
Server: Accepted new connection 69
Server: Accepted new connection 70
Server: Accepted new connection 71
Server: Accepted new connection 72
Server: Accepted new connection 73
Server: Accepted new connection 74
Server: Accepted new connection 75
Server: Accepted new connection 76
Server: Accepted new connection 77
Server: Accepted new connection 78
Server: Accepted new connection 79
Server: Accepted new connection 80
Server: Accepted new connection 81
Server: Accepted new connection 82
Server: Accepted new connection 83
Server: Accepted new connection 84
Server: Accepted new connection 85
Server: Accepted new connection 86
Server: Accepted new connection 87
Server: Accepted new connection 88
Server: Accepted new connection 89
Server: Accepted new connection 90
Server: Accepted new connection 91
Server: Accepted new connection 92
Server: Accepted new connection 93
Server: Accepted new connection 94
Server: Accepted new connection 95
Server: Accepted new connection 96
Server: Accepted new connection 97
Server: Accepted new connection 98
Server: Accepted new connection 99
Server: Accepted new connection 100
Connections: 100 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 00