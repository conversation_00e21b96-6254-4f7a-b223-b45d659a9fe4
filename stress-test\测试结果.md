PS C:\Users\<USER>\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-client.exe --host=127.0.0.1 --port=8080 --connections=100 --duration=30
Starting Mock Game Client
Target: 127.0.0.1:8080
Connections: 100
IO threads: 16
Duration: 30s

Creating connections...
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0Created 100/100 connections
Starting heartbeat...
Test running for 30 seconds...
Connections: 100 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 00




PS C:\Users\<USER>\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin>  test-transport.exe
test-transport.exe : 无法将“test-transport.exe”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，
如果包括路径，请确保路径正确，然后再试一次。
所在位置 行:1 字符: 2
+  test-transport.exe
+  ~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (test-transport.exe:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException


Suggestion [3,General]: 找不到命令 test-transport.exe，但它确实存在于当前位置。默认情况下，Windows PowerShell 不会从当前位置加载命令。如果信任此命令，请改为键入“.\test-transport.exe”。有关详细信息，请参阅 "get-help about_Command_Precedence"。
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./ test-transport.exe
./ : 无法将“./”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保路径正确，然
后再试一次。
所在位置 行:1 字符: 1
+ ./ test-transport.exe
+ ~~
    + CategoryInfo          : ObjectNotFound: (./:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./test-transport.exe
Testing DirectTransport...
鉁?Initial state: not connected
鉁?DirectTransport basic test passed
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> mock-server.exe --port=8080 --max-connections=1000 --threads=2
mock-server.exe : 无法将“mock-server.exe”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包
括路径，请确保路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ mock-server.exe --port=8080 --max-connections=1000 --threads=2
+ ~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (mock-server.exe:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException


Suggestion [3,General]: 找不到命令 mock-server.exe，但它确实存在于当前位置。默认情况下，Windows PowerShell 不会从当前位置加载命令。如果信任此命令，请改为键入“.\mock-server.exe”。有关详细信息，请参阅 "get-help about_Command_Precedence"。
PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-server.exe --port=8080 --max-connections=1000 --threads=2
Starting Mock Game Server
Host: 0.0.0.0
Port: 8080
Max connections: 1000
IO threads: 2

Server started on 0.0.0.0:8080, max connections: 1000
Server running. Press Ctrl+C to stop...
Connections: 100 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 00


PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-server.exe --port=8080 --max-connections=1000
Starting Mock Game Server
Host: 0.0.0.0
Port: 8080
Max connections: 1000
IO threads: 16

Server started on 0.0.0.0:8080, max connections: 1000
Server running. Press Ctrl+C to stop...
Connections: 100 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 00

PS E:\study\cursor\Test-tool\stress-test\out\build\x64-Debug\bin> ./mock-client.exe --connections=100 --duration=60
Starting Mock Game Client
Target: 127.0.0.1:8080
Connections: 100
IO threads: 16
Duration: 60s

Creating connections...
Connections: 0 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 0Created 100/100 connections
Starting heartbeat...
Test running for 60 seconds...
Connections: 100 | CPS: 0.0 | Throughput: 0.00 Mbps | Sent: 0 | Recv: 00